<?php
/**
 * Functions for extracting the rate file.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Get array of filenames for parcelforce CSV files.
 *
 * @return array
 *
 * @throws Exception When filenames is empty.
 */
function get_parcelforce_international_zone_files() {
	$zone_files_pattern = __DIR__ . '/rate-data/parcelforce-international/*.csv';
	$filenames          = glob( $zone_files_pattern );

	if ( empty( $filenames ) ) {
		// phpcs:ignore WordPress.Security.EscapeOutput.ExceptionNotEscaped --- For testing only
		throw new Exception( 'No files found for pattern: ' . $zone_files_pattern );
	}
	return $filenames;
}

/**
 * Open CSV file and parse into an associative array.
 *
 * @param string $filename CSV file name/path.
 * @return array
 */
function get_csv_as_array( $filename ) {
	$raw_csv_rows = @file( $filename ); // phpcs:ignore WordPress.PHP.NoSilencedErrors.Discouraged --- only for testing

	if ( false === $raw_csv_rows ) {
		// translators: %1$s is a filename.
		echo sprintf( esc_html__( 'Could not open: %1$s', 'woocommerce-shipping-royalmail' ), esc_html( $filename ) ) . "\n";
		return array();
	}

	$header_row = array_shift( $raw_csv_rows );
	$fields     = str_getcsv( $header_row );

	$parsed_data = array();
	foreach ( $raw_csv_rows as $row ) {
		$parsed_row = array();
		$values     = str_getcsv( $row );
		foreach ( $values as $key => $value ) {
			$field_name                = $fields[ $key ];
			$parsed_row[ $field_name ] = $value;
		}
		$parsed_data[] = $parsed_row;
	}
	return $parsed_data;
}

/**
 * Reorganize zone columns as they are expected for serialize_service_array().
 *
 * @param array $rate_data CSV file data.
 * @return array
 */
function group_rate_data_by_zone( $rate_data ) {
	$service_data = array();
	foreach ( $rate_data as $row ) {
		// Get weight value and remove it from row.
		$weight = strval( reset( $row ) );
		unset( $row[ key( $row ) ] );

		foreach ( $row as $zone_number => $rate ) {
			if ( ! isset( $service_data[ $zone_number ] ) ) {
				$service_data[ $zone_number ] = array();
			}
			$service_data[ $zone_number ][] = array(
				$weight => $rate,
			);
		}
	}
	return $service_data;
}


/**
 * Post process zone CSV data.
 *
 * Removes "Add 0.5kg" row and cleans out extraneous text/symbols.
 *
 * @param array $data Associative array parsed from CSV file.
 * @return array
 *
 * @throws Exception When data does not have weight field.
 */
function post_process_zone_data( $data ) {
	$last_row = end( $data );
	reset( $data );
	// Quick check for expected data format.
	if ( ! isset( $last_row['weight'] ) ) {
		throw new Exception( "There's no 'weight' field in the csv data. Something went wrong here. Does the CSV file contain expected fields?" );
	}
	// Remove 'Add 0.5kg' row.
	if ( 'Add per 0.5kg' === $last_row['weight'] ) {
		array_pop( $data );
	}

	// Remove extra text and units.
	$removals = array(
		'up to ',
		'kg',
		'£',
	);
	foreach ( $data as $row_number => $row ) {
		$data[ $row_number ] = str_replace( $removals, '', $row );
	}
	return group_rate_data_by_zone( $data );
}

/**
 * Converts the service data associative array into a string representing the rates.
 *
 * The output of this function can be pasted into the extension rate files.
 *
 * @param string $service_data Filename for zone CSV.
 * @return int
 */
function serialize_service_array( $service_data ) {
	$array_string = '';

	$array_string .= sprintf( "array(\n" );
	foreach ( $service_data as $zone_number => $zone ) {
		$zone_number   = intval( $zone_number );
		$array_string .= sprintf( "\t\t\t'%d' => array(\n", $zone_number );
		foreach ( $zone as $rate ) {
			// Convert weight to grams.
			$weight_kg   = floatval( key( $rate ) );
			$weight_band = intval( $weight_kg * 1000 );

			// Convert rate to cents.
			$rate_raw      = floatval( array_shift( $rate ) );
			$rate_price    = $rate_raw * 100;
			$array_string .= sprintf( "\t\t\t\t%-5d => %s,\n", $weight_band, $rate_price );
		}
		$array_string .= sprintf( "\t\t\t),\n" );
	}
	$array_string .= sprintf( "\t\t)," );

	return $array_string;
}

/**
 * Parse service name from the file name.
 *
 * @param string $service_filename Filename for service CSV.
 * @return string
 *
 * @throws Exception When service name is empty.
 */
function get_service_name_from_filename( $service_filename ) {
	preg_match( '/.*?(\w+?)\.csv/', $service_filename, $matches );
	$service_name = $matches[1];
	if ( empty( $service_name ) ) {
		// phpcs:ignore WordPress.Security.EscapeOutput.ExceptionNotEscaped --- For testing only
		throw new Exception( 'Something went wrong here. No service name found from filename: ' . $service_filename );
	}
	return $service_name;
}

/**
 * Create output file with provided out_data.
 *
 * @param string $output_directory Directory to write file to.
 * @param string $service_name     Service name (for file naming).
 * @param string $out_data         Data to write to file.
 *
 * @throws Exception When no content on the file.
 */
function output_array_string_to_file( $output_directory, $service_name, $out_data ) {
	$output_filename = $output_directory . '/' . $service_name;
	if ( ! file_put_contents( $output_filename, $out_data ) ) { // phpcs:ignore --- only for testing
		// phpcs:ignore WordPress.Security.EscapeOutput.ExceptionNotEscaped --- For testing only
		throw new Exception( 'Could not write output data for service "' . $service_name . '", path: ' . $output_filename );
	}
	printf( "Wrote output array for service '%s' at: %s\n", esc_html( $service_name ), esc_html( $output_filename ) );
}



$output_directory = __DIR__ . '/out';
if ( ! file_exists( $output_directory ) ) {
	// phpcs:ignore WordPress.WP.AlternativeFunctions.file_system_operations_mkdir --- For testing only
	if ( ! mkdir( $output_directory ) ) {
		// phpcs:ignore WordPress.Security.EscapeOutput.ExceptionNotEscaped --- For testing only
		throw new Exception( 'Could not make output directory: ' . $output_directory );
	}
}

echo( "Processing Parcelforce rate CSVs.\n" );

$international_rate_files = get_parcelforce_international_zone_files();

$service_data = array();
foreach ( $international_rate_files as $rate_filename ) {
	$service_name      = get_service_name_from_filename( $rate_filename );
	$data              = get_csv_as_array( $rate_filename );
	$data              = post_process_zone_data( $data );
	$data_array_string = serialize_service_array( $data );
	output_array_string_to_file( $output_directory, $service_name, $data_array_string );
}

echo( "Done. Data can now be copied from ./out directory and pasted into appropriate rate arrays.\n" );
