The rate-extract.php script was created to automate some steps for gathering rates from Parcelforce.

Parcelforce publishes most of their rates in a PDF file which would otherwise need to be manually
transcribed to the source code. An application called Tabula can be used to extract the tables
from the PDF file so they can be copied into CSV files. The rate-extract.php script can then be used
to proccess the CSV files and convert the data to PHP arrays that can be pasted into the source code.
It's a far-from-perfect solution but hopefully will save us some time when updating rates.

To update Parcelforce rates:

 1. Install and setup Tabula (https://tabula.technology/).
 2. Use Tabula to extract rate table data so it can be copied and pasted.
 3. Update CSV rate files in ./rate-data/parcelforce-international directory.
 4. Run `php rate-extract.php`.
 5. <PERSON><PERSON> generated PHP array strings from generated files in ./out directory.
 6. Paste new PHP array strings into `rates/international/class-royalmail-rate-parcelforce-*.php` files.
