<?xml version="1.0" encoding="utf-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" backupGlobals="false" colors="true" processIsolation="false" displayDetailsOnTestsThatTriggerWarnings="true" stopOnFailure="true" bootstrap="tests/phpunit/bootstrap.php" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.3/phpunit.xsd" cacheDirectory=".phpunit.cache">
  <coverage includeUncoveredFiles="true"/>
  <testsuites>
    <testsuite name="RoyalMail">
      <directory suffix="_Test.php">./tests/phpunit</directory>
    </testsuite>
  </testsuites>
  <groups>
    <exclude>
      <group>functional</group>
    </exclude>
  </groups>
  <source>
    <include>
      <directory suffix=".php">.</directory>
    </include>
    <exclude>
      <directory suffix=".php">./woo-includes/</directory>
      <directory>./tests/</directory>
    </exclude>
  </source>
</phpunit>
