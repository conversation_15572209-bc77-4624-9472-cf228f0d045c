### Create Bug Reports

If you or customers find a bug, let us know by creating a new issue. You can [check our requirements to create good bug reports here](https://extendomattic.wordpress.com/triaging-issues/#issues-requirements).

### Write and submit a patch

If you'd like to fix a bug, you can submit a Pull Request. If possible, raises an issue first and link the issue in your [commit message](https://help.github.com/articles/closing-issues-via-commit-messages/) or [PR's body](https://github.com/blog/1506-closing-issues-via-pull-requests).

When creating Pull Requests, remember:

- [Check In Early, Check In Often](http://blog.codinghorror.com/check-in-early-check-in-often/).
- Write [good commit messages](http://tbaggery.com/2008/04/19/a-note-about-git-commit-messages.html).
- Respect the [Best practices for WordPress development](http://jetpack.com/contribute/#practices).

