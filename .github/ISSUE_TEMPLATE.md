Before submitting a bug report be sure you have checked out [our support page](https://extendomattic.wordpress.com/extendables-support/) for information on labeling and other support channels.

<!-- You MUST add labels or this issue will be ignored. Please add a type (bug/enhancement/technical debt) and a priority (high/low). If these are not added, the issue will not be responded to or addressed. -->

**Describe the bug**
A clear and concise description of what the bug is. Please be as descriptive as possible.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Screenshots**
If applicable, add screenshots to help explain your problem.

**Expected behavior**
A clear and concise description of what you expected to happen.

**Isolating the problem (mark completed items with an [x]):**
- [ ] I have deactivated other plugins and confirmed this bug occurs when only the extension is active.
- [ ] I can reproduce this bug consistently using the steps above.

**WordPress Environment**
<details>
```
Copy and paste the system status report from **WooCommerce > System Status** in WordPress admin.
```
</details>
