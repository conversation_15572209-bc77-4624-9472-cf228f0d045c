name: WooCommerce Core Release Smoke Test
on:
  workflow_dispatch:
permissions:
  contents: read
  pull-requests: write
jobs:
  build:
    name: Build artifact (zip file)
    uses: ./.github/workflows/build.yml
    with:
      plugin_name: woocommerce-shipping-royalmail
    secrets:
      COMPOSER_AUTH: ${{ secrets.COMPOSER_AUTH }}

  qit_tests:
    name: QIT Tests
    needs: build
    runs-on: ubuntu-latest
    env:
      NO_COLOR: 1
      QIT_DISABLE_ONBOARDING: yes
      PLUGIN_NAME: ${{ needs.build.outputs.plugin_name }}
    steps:
      - name: Download plugin zip
        uses: actions/download-artifact@v4
        with:
          name: ${{ needs.build.outputs.plugin_name }}

      - name: Install QIT via composer
        env:
          COMPOSER_AUTH: '{"github-oauth":{"github.com": "${{ secrets.COMPOSER_AUTH }}"}}'
        run: composer require woocommerce/qit-cli

      - name: Add Partner
        run: ./vendor/bin/qit partner:add --user='${{ secrets.PARTNER_USER }}' --application_password='${{ secrets.PARTNER_SECRET }}'

      - name: Create results dir
        run: mkdir ${{ github.workspace }}/qit-results

      - name: Run activation test
        if: always()
        id: run-activation-test
        run: ./vendor/bin/qit run:activation ${{ env.PLUGIN_NAME }} --zip=${{ env.PLUGIN_NAME }}.zip --wait > ${{ github.workspace }}/qit-results/qit-activation-results.txt

      - name: Echo activation results
        if: always()
        run: cat ${{ github.workspace }}/qit-results/qit-activation-results.txt

      - name: Run Woo API Test
        if: always()
        id: run-woo-api-test
        run: ./vendor/bin/qit run:woo-api ${{ env.PLUGIN_NAME }} --zip=${{ env.PLUGIN_NAME }}.zip --wordpress_version=stable --woocommerce_version=rc --wait > ${{ github.workspace }}/qit-results/qit-woo-api-results.txt

      - name: Echo Woo API results
        if: always()
        run: cat ${{ github.workspace }}/qit-results/qit-woo-api-results.txt

      # This can take up to 1h to complete.
      - name: Run Woo E2E Test
        if: always()
        id: run-woo-e2e-test
        run: ./vendor/bin/qit run:woo-e2e ${{ env.PLUGIN_NAME }} --zip=${{ env.PLUGIN_NAME }}.zip --wordpress_version=stable --woocommerce_version=rc --wait > ${{ github.workspace }}/qit-results/qit-woo-e2e-results.txt

      - name: Echo Woo E2E results
        if: always()
        run: cat ${{ github.workspace }}/qit-results/qit-woo-e2e-results.txt

      - name: Upload results
        if: always()
        uses: actions/upload-artifact@v4
        with:
          name: qit-results
          path: ${{ github.workspace }}/qit-results/
