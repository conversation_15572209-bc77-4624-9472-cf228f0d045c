<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase WordPress.Files.FileName.InvalidClassFileName
/**
 * Royalmail rates test file.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Royalmail rates test class.
 *
 * @extends Royalmail_TestCase
 */
class WC_Shipping_Royalmail_Rates_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		global $royal_mail_box_sizes;
		$royal_mail_box_sizes = null;
	}

	/**
	 * Tearing down the test.
	 */
	protected function tearDown(): void {
		\Spies\finish_spying();
	}

	/**
	 * Test the debug message.
	 */
	public function test_debug() {
		$add_notice = \Spies\get_spy_for( 'wc_add_notice' );

		$rates    = new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( 'GB' ), 'per_item' );
		$is_debug = $rates->debug( 'debug message' );
		$this->assertEquals( false, $is_debug );

		$expectation = \Spies\expect_spy( $add_notice )->times( 0 );
		$expectation->verify();

		// This constant will make `wc_add_notice` gets invoked.
		define( 'WC_ROYALMAIL_DEBUG', true );
		$is_debug = $rates->debug( '1st debug message' );
		$this->assertEquals( true, $is_debug );

		$is_debug = $rates->debug( '2nd debug message' );
		$this->assertEquals( true, $is_debug );

		$expectation = \Spies\expect_spy( $add_notice )->times( 2 );
		$expectation->verify();
	}

	/**
	 * Testing the plugin path.
	 */
	public function test_plugin_path() {
		$plugin_dir_path   = \Spies\get_spy_for( 'plugin_dir_path' );
		$untrailingslashit = \Spies\get_spy_for( 'untrailingslashit' );

		$rates             = new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( 'GB' ), 'per_item' );
		$rates_plugin_path = $rates->plugin_path();

		$expectation = \Spies\expect_spy( $plugin_dir_path )->times( 1 );
		$expectation->verify();
		$expectation = \Spies\expect_spy( $untrailingslashit )->times( 1 );
		$expectation->verify();

		$current_path = untrailingslashit( plugin_dir_path( __DIR__ ) );
		$this->assertEquals( $current_path, $rates_plugin_path );
	}

	/**
	 * Test for getting the service type.
	 */
	public function test_get_service_type() {
		$rates = new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( 'GB' ), 'per_item' );
		$this->assertEquals( 'uk', $rates->get_service_type() );

		$rates = new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( 'DE' ), 'per_item' );
		$this->assertEquals( 'international', $rates->get_service_type() );

		$rates = new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( 'US' ), 'per_item' );
		$this->assertEquals( 'international', $rates->get_service_type() );
	}

	/**
	 * Test to get the services.
	 */
	public function test_get_services() {
		$rates = new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( 'GB' ), 'per_item', array(), '', 'regular' );
		$this->assertEquals(
			array(
				'first-class',
				'second-class',
				'first-class-signed',
				'second-class-signed',
				'special-delivery-9am',
				'special-delivery-1pm',
				'tracked-24',
				'tracked-24-signed',
				'tracked-48',
				'tracked-48-signed',
				'parcelforce-express-9',
				'parcelforce-express-10',
				'parcelforce-express-am',
				'parcelforce-express-24',
				'parcelforce-express-48',
				'parcelforce-express-48-large',
			),
			$rates->get_services()
		);

		$rates = new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( 'US' ), 'per_item', array(), '', 'regular' );
		$this->assertEquals(
			array(
				'international-tracked',
				'international-tracked-signed',
				'international-standard',
				'international-economy',
				'international-signed',
				'parcelforce-irelandexpress',
				'parcelforce-globaleconomy',
				'parcelforce-globalexpress',
				'parcelforce-globalpriority',
				'parcelforce-globalvalue',
			),
			$rates->get_services()
		);

		$rates = new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( 'GB' ), 'per_item', array(), '', 'online' );
		$this->assertEquals(
			array(
				'first-class',
				'second-class',
				'first-class-signed',
				'second-class-signed',
				'special-delivery-1pm',
				'tracked-24',
				'tracked-24-signed',
				'tracked-24-age-verification',
				'tracked-48',
				'tracked-48-signed',
				'tracked-48-age-verification',
			),
			$rates->get_services()
		);

		$rates = new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( 'US' ), 'per_item', array(), '', 'online' );
		$this->assertEquals(
			array(
				'international-tracked',
				'international-tracked-signed',
				'international-standard',
				'international-economy',
				'international-signed',
			),
			$rates->get_services()
		);
	}

	/**
	 * Test for getting the service class path.
	 */
	public function test_get_service_class_path() {
		\Spies\stub_function( 'plugin_dir_path' )->that_returns( '' );
		\Spies\stub_function( 'untrailingslashit' )->that_returns( '' );

		$rates = new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( 'GB' ), 'per_item', array(), '', 'regular' );
		$this->assertEquals( '/includes/rates/uk/regular/class-royalmail-regular-first-class.php', $rates->get_service_class_path( 'first-class' ) );
		$this->assertEquals( '/includes/rates/uk/regular/class-royalmail-regular-second-class.php', $rates->get_service_class_path( 'second-class' ) );
		$this->assertEquals( '/includes/rates/uk/regular/class-royalmail-regular-special-delivery-9am.php', $rates->get_service_class_path( 'special-delivery-9am' ) );
		$this->assertEquals( '/includes/rates/uk/regular/class-royalmail-regular-special-delivery-1pm.php', $rates->get_service_class_path( 'special-delivery-1pm' ) );
		$this->assertEquals( '/includes/rates/uk/regular/class-royalmail-regular-parcelforce-express-9.php', $rates->get_service_class_path( 'parcelforce-express-9' ) );
		$this->assertEquals( '/includes/rates/uk/regular/class-royalmail-regular-parcelforce-express-10.php', $rates->get_service_class_path( 'parcelforce-express-10' ) );
		$this->assertEquals( '/includes/rates/uk/regular/class-royalmail-regular-parcelforce-express-am.php', $rates->get_service_class_path( 'parcelforce-express-am' ) );
		$this->assertEquals( '/includes/rates/uk/regular/class-royalmail-regular-parcelforce-express-24.php', $rates->get_service_class_path( 'parcelforce-express-24' ) );
		$this->assertEquals( '/includes/rates/uk/regular/class-royalmail-regular-parcelforce-express-48.php', $rates->get_service_class_path( 'parcelforce-express-48' ) );
		$this->assertEquals( '/includes/rates/uk/regular/class-royalmail-regular-parcelforce-express-48-large.php', $rates->get_service_class_path( 'parcelforce-express-48-large' ) );

		$rates = new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( 'US' ), 'per_item', array(), '', 'regular' );
		$this->assertEquals( '/includes/rates/international/regular/class-royalmail-regular-international-tracked.php', $rates->get_service_class_path( 'international-tracked' ) );
		$this->assertEquals( '/includes/rates/international/regular/class-royalmail-regular-international-standard.php', $rates->get_service_class_path( 'international-standard' ) );
		$this->assertEquals( '/includes/rates/international/regular/class-royalmail-regular-international-economy.php', $rates->get_service_class_path( 'international-economy' ) );
		$this->assertEquals( '/includes/rates/international/regular/class-royalmail-regular-international-signed.php', $rates->get_service_class_path( 'international-signed' ) );
		$this->assertEquals( '/includes/rates/international/regular/class-royalmail-regular-parcelforce-globaleconomy.php', $rates->get_service_class_path( 'parcelforce-globaleconomy' ) );
		$this->assertEquals( '/includes/rates/international/regular/class-royalmail-regular-parcelforce-globalexpress.php', $rates->get_service_class_path( 'parcelforce-globalexpress' ) );
		$this->assertEquals( '/includes/rates/international/regular/class-royalmail-regular-parcelforce-globalpriority.php', $rates->get_service_class_path( 'parcelforce-globalpriority' ) );
		$this->assertEquals( '/includes/rates/international/regular/class-royalmail-regular-parcelforce-globalvalue.php', $rates->get_service_class_path( 'parcelforce-globalvalue' ) );
		$this->assertEquals( '/includes/rates/international/regular/class-royalmail-regular-parcelforce-irelandexpress.php', $rates->get_service_class_path( 'parcelforce-irelandexpress' ) );
	}

	/**
	 * Test for getting the service class name.
	 */
	public function test_get_service_class_name() {
		\Spies\stub_function( 'plugin_dir_path' )->that_returns( '' );
		\Spies\stub_function( 'untrailingslashit' )->that_returns( '' );
		$rates = new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( 'UK' ), 'per_item', array(), '', 'regular' );

		$this->assertEquals( 'RoyalMail_Regular_First_Class', $rates->get_service_class_name( 'first-class' ) );
		$this->assertEquals( 'RoyalMail_Regular_Second_Class', $rates->get_service_class_name( 'second-class' ) );
		$this->assertEquals( 'RoyalMail_Regular_Special_Delivery_9am', $rates->get_service_class_name( 'special-delivery-9am' ) );
		$this->assertEquals( 'RoyalMail_Regular_Special_Delivery_1pm', $rates->get_service_class_name( 'special-delivery-1pm' ) );
		$this->assertEquals( 'RoyalMail_Regular_Parcelforce_Express_9', $rates->get_service_class_name( 'parcelforce-express-9' ) );
		$this->assertEquals( 'RoyalMail_Regular_Parcelforce_Express_10', $rates->get_service_class_name( 'parcelforce-express-10' ) );
		$this->assertEquals( 'RoyalMail_Regular_Parcelforce_Express_Am', $rates->get_service_class_name( 'parcelforce-express-am' ) );
		$this->assertEquals( 'RoyalMail_Regular_Parcelforce_Express_24', $rates->get_service_class_name( 'parcelforce-express-24' ) );
		$this->assertEquals( 'RoyalMail_Regular_Parcelforce_Express_48', $rates->get_service_class_name( 'parcelforce-express-48' ) );
		$this->assertEquals( 'RoyalMail_Regular_Parcelforce_Express_48_Large', $rates->get_service_class_name( 'parcelforce-express-48-large' ) );

		$rates = new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( 'US' ), 'per_item', array(), '', 'regular' );

		$this->assertEquals( 'RoyalMail_Regular_International_Tracked', $rates->get_service_class_name( 'international-tracked' ) );
		$this->assertEquals( 'RoyalMail_Regular_International_Tracked_Signed', $rates->get_service_class_name( 'international-tracked-signed' ) );
		$this->assertEquals( 'RoyalMail_Regular_International_Standard', $rates->get_service_class_name( 'international-standard' ) );
		$this->assertEquals( 'RoyalMail_Regular_International_Signed', $rates->get_service_class_name( 'international-signed' ) );
		$this->assertEquals( 'RoyalMail_Regular_International_Economy', $rates->get_service_class_name( 'international-economy' ) );
		$this->assertEquals( 'RoyalMail_Regular_Parcelforce_Irelandexpress', $rates->get_service_class_name( 'parcelforce-irelandexpress' ) );
		$this->assertEquals( 'RoyalMail_Regular_Parcelforce_Globaleconomy', $rates->get_service_class_name( 'parcelforce-globaleconomy' ) );
		$this->assertEquals( 'RoyalMail_Regular_Parcelforce_Globalexpress', $rates->get_service_class_name( 'parcelforce-globalexpress' ) );
		$this->assertEquals( 'RoyalMail_Regular_Parcelforce_Globalpriority', $rates->get_service_class_name( 'parcelforce-globalpriority' ) );
		$this->assertEquals( 'RoyalMail_Regular_Parcelforce_Globalvalue', $rates->get_service_class_name( 'parcelforce-globalvalue' ) );

		$rates = new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( 'UK' ), 'per_item', array(), '', 'online' );

		$this->assertEquals( 'RoyalMail_Online_First_Class', $rates->get_service_class_name( 'first-class' ) );
		$this->assertEquals( 'RoyalMail_Online_Second_Class', $rates->get_service_class_name( 'second-class' ) );
		$this->assertEquals( 'RoyalMail_Online_First_Class_Signed', $rates->get_service_class_name( 'first-class-signed' ) );
		$this->assertEquals( 'RoyalMail_Online_Second_Class_Signed', $rates->get_service_class_name( 'second-class-signed' ) );
		$this->assertEquals( 'RoyalMail_Online_Special_Delivery_1pm', $rates->get_service_class_name( 'special-delivery-1pm' ) );
		$this->assertEquals( 'RoyalMail_Online_Tracked_24', $rates->get_service_class_name( 'tracked-24' ) );
		$this->assertEquals( 'RoyalMail_Online_Tracked_48', $rates->get_service_class_name( 'tracked-48' ) );

		$rates = new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( 'US' ), 'per_item', array(), '', 'online' );

		$this->assertEquals( 'RoyalMail_Online_International_Tracked', $rates->get_service_class_name( 'international-tracked' ) );
		$this->assertEquals( 'RoyalMail_Online_International_Tracked_Signed', $rates->get_service_class_name( 'international-tracked-signed' ) );
		$this->assertEquals( 'RoyalMail_Online_International_Standard', $rates->get_service_class_name( 'international-standard' ) );
		$this->assertEquals( 'RoyalMail_Online_International_Signed', $rates->get_service_class_name( 'international-signed' ) );
		$this->assertEquals( 'RoyalMail_Online_International_Economy', $rates->get_service_class_name( 'international-economy' ) );
	}

	/**
	 * Test for getting the quotes.
	 */
	public function test_get_quotes() {
		$rates = new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( 'GB' ), 'per_item' );
		$this->assertEquals( 'per_item', $rates->packing_method );
		$this->assertEquals( null, $rates->get_quotes() );
	}

	/**
	 * Test the quotes on UK.
	 */
	public function test_get_quotes_within_uk() {
		$this->verify_get_quotes_to( 'GB' );
	}

	/**
	 * Test the quotes on international zone 4.
	 */
	public function test_get_quotes_channel_islands_zone_4() {
		$this->verify_get_quotes_to( 'JE', '4' );
	}

	/**
	 * Test the quotes on international zone 7.
	 */
	public function test_get_quotes_international_zone_7() {
		$this->verify_get_quotes_to( 'DE', '7' );
		$this->verify_get_quotes_to( 'FR', '7' );
		$this->verify_get_quotes_to( 'DK', '7' );
	}

	/**
	 * Test the quotes on international zone 8.
	 */
	public function test_get_quotes_international_zone_8() {
		$this->verify_get_quotes_to( 'IT', '8' );
		$this->verify_get_quotes_to( 'ES', '8' );
		$this->verify_get_quotes_to( 'PT', '8' );
		$this->verify_get_quotes_to( 'GR', '8' );
	}

	/**
	 * Test the quotes on international zone 9.
	 */
	public function test_get_quotes_international_zone_9() {
		$this->verify_get_quotes_to( 'RU', '9' );
		$this->verify_get_quotes_to( 'CH', '9' );
		$this->verify_get_quotes_to( 'TR', '9' );
		$this->verify_get_quotes_to( 'VA', '9' );
	}

	/**
	 * Test the quotes on international zone 10.
	 */
	public function test_get_quotes_international_zone_10() {
		$this->verify_get_quotes_to( 'US', '10' );
		// phpcs:ignore
		// $this->verify_get_quotes_to( 'CA', '10' );
	}

	/**
	 * Test the quotes on international zone 11.
	 */
	public function test_get_quotes_international_zone_11() {
		$this->verify_get_quotes_to( 'CN', '11' );
		$this->verify_get_quotes_to( 'MN', '11' );
		$this->verify_get_quotes_to( 'KP', '11' );
		$this->verify_get_quotes_to( 'TW', '11' );
		$this->verify_get_quotes_to( 'BN', '11' );
		$this->verify_get_quotes_to( 'TL', '11' );
		$this->verify_get_quotes_to( 'MM', '11' );
		$this->verify_get_quotes_to( 'PH', '11' );
		$this->verify_get_quotes_to( 'VN', '11' );
	}

	/**
	 * Verifying the quotes destination.
	 *
	 * @param string $destination Country destination.
	 * @param string $zone Rate zone.
	 *
	 * @return void
	 */
	private function verify_get_quotes_to( $destination, $zone = '' ) {
		$this->prepare_test_quotes( $destination, $zone );
		$type = '';

		switch ( $destination ) {
			case 'GB':
				$type = 'uk';
				break;

			case 'JE':
				$type = 'channel_islands';
				break;

			default:
				$type = 'international_zone_' . $zone;
				break;
		}

		foreach ( $this->get_test_cases( $destination ) as $case ) {
			foreach ( $this->get_test_data( $type . '_' . $case ) as $data ) {
				$rates = new WC_Shipping_Royalmail_Rates(
					$this->to_package( $destination, $data ),
					$this->get_packing_method_from_test_case( $case )
				);

				$this->assertEquals(
					$data['expected_quotes'],
					$this->round_get_quotes( $rates->get_quotes() ),
					$this->get_quotes_assert_desc( $data, $destination )
				);
			}
		}
	}

	/**
	 * Get test cases based on destination.
	 *
	 * @param string $destination Country destination.
	 */
	private function get_test_cases( $destination ) {
		$cases = array(
			'per_item_single_item',
			'per_item_multiple_items',
		);

		if ( ! in_array( $destination, array( 'GB', 'JE' ), true ) ) {
			$cases = array_merge(
				$cases,
				array(
					'box_packing_single_item',
					'box_packing_multiple_items',
				)
			);
		}

		return $cases;
	}

	/**
	 * Get packing method from test case.
	 *
	 * @param string $test_case Packing method from test case.
	 *
	 * @return string
	 */
	private function get_packing_method_from_test_case( $test_case ) {
		return false !== strpos( $test_case, 'per_item' ) ? 'per_item' : 'box_packing';
	}

	/**
	 * Preparing the test quotes.
	 *
	 * @param string $destination Country destination.
	 * @param int    $zone Rate zone.
	 */
	private function prepare_test_quotes( $destination, $zone ) {
		\Spies\stub_function( 'plugin_dir_path' )->that_returns( '' );
		\Spies\stub_function( 'untrailingslashit' )->that_returns( __DIR__ . '/../../' );
		\Spies\stub_function( 'wc_add_notice' )->that_returns( '' );

		require_once 'tests/phpunit/mock.php';

		$whitelisted_eur = ( ! in_array( intval( $zone ), array( 10, 11, 12 ), true ) )
			? array( $destination )
			: array();

		mock_wc( mock_wc_countries( $whitelisted_eur ) );
	}

	/**
	 * Get description from data.
	 *
	 * @param array  $data Cart data.
	 * @param string $destination Country destination.
	 */
	private function get_quotes_assert_desc( $data, $destination ) {
		$desc = ! empty( $data['description'] ) ? $data['description'] : array();
		if ( ! is_array( $desc ) ) {
			$desc = array( $desc );
		}

		$desc[] = '';
		$desc[] = 'Destination: ' . $destination;
		$desc[] = '';
		$desc[] = sprintf( 'Package contents: %s', print_r( $data['contents'], true ) ); // phpcs:ignore WordPress.PHP.DevelopmentFunctions.error_log_print_r --- only for testing

		return implode( "\n", $desc );
	}

	/**
	 * Formatting the quotes.
	 *
	 * @param array $quotes Quotes.
	 */
	private function round_get_quotes( $quotes ) {
		foreach ( $quotes as $id => $quote ) {
			$quotes[ $id ] = number_format( $quote, 2, '.', '' );
		}
		return $quotes;
	}

	/**
	 * Create a package to destination.
	 *
	 * @param string $destination Country destination.
	 * @param array  $data Package data.
	 *
	 * @return array
	 */
	private function to_package( $destination, $data ) {
		$package = $this->get_empty_package_to( $destination );

		foreach ( $data['contents'] as $index => $content ) {
			$package['contents'][ $index + 1 ] = array(
				'quantity' => $content['quantity'],
				'data'     => new WC_Product( $content ),
			);
		}

		return $package;
	}
}
