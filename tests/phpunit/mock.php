<?php // phpcs:ignore WordPress.Files.FileName.InvalidClassFileName
/**
 * Mock function and class.
 *
 * @package WC_Shipping_Royalmail
 */

// phpcs:disable Generic.Files.OneObjectStructurePerFile.MultipleFound --- only for mock
/**
 * Mock required functions and classes.
 *
 * First arg for `wc_get_dimension` must be in 'mm' and first arg for `wc_get_weight`
 * must be in 'g'.
 *
 * Mock of
 */
class WC_Product {
	/**
	 * Class constructor.
	 *
	 * @param array $args Constructor args.
	 */
	public function __construct( array $args ) {
		$props                = array( 'weight', 'length', 'width', 'height', 'price' );
		$this->needs_shipping = true;
		foreach ( $props as $prop ) {
			$this->{$prop} = ! empty( $args[ $prop ] ) ? $args[ $prop ] : 0;
			if ( ! $this->{$prop} ) {
				$this->needs_shipping = false;
			}
		}
	}

	/**
	 * Call magic method.
	 *
	 * @param string $name Name of the method.
	 * @param mixed  $args Method's args.
	 *
	 * @return mixed.
	 */
	public function __call( $name, $args ) {
		switch ( $name ) {
			case 'get_weight':
				return $this->weight;
			case 'get_length':
				return $this->length;
			case 'get_width':
				return $this->width;
			case 'get_height':
				return $this->height;
			case 'get_price':
				return $this->price;
			case 'needs_shipping':
				return $this->needs_shipping;
		}

		return null;
	}
}

/**
 * Mock class for mocking the `WC_Shipping_Royalmail_Admin` class.
 */
class WC_Shipping_Royalmail_Admin {

	const META_KEY_PRINTED_PAPERS = '_shipping-royalmail-printed-papers';

	const META_KEY_BOOK = '_shipping-royalmail-book';

	const META_KEY_TUBE = '_shipping-royalmail-tube';

	const LABEL_PRINTED_PAPERS = 'Printed Papers';

	const LABEL_BOOK = 'Book';

	const LABEL_TUBE = 'Tube';
}
// phpcs:enable Generic.Files.OneObjectStructurePerFile.MultipleFound

/**
 * Mock function mocking the WooCommerce `wc_get_weight()`.
 *
 * @param float  $value Weight.
 * @param string $__ Unit to convert to.
 *
 * @return float.
 */
function wc_get_weight( $value, $__ ) { // phpcs:ignore --- Only for mocking
	return $value;
}

/**
 * Mock function mocking the WooCommerce `wc_get_dimension()`.
 *
 * @param float  $value Dimension.
 * @param string $__ Unit to convert to.
 *
 * @return float.
 */
function wc_get_dimension( $value, $__ ) { // phpcs:ignore Generic.CodeAnalysis.UnusedFunctionParameter.FoundAfterLastUsed --- Only for mocking
	return $value;
}

/**
 * Mock function mocking the WooCommerce.
 *
 * @param array $wc_countries List of WC countries.
 *
 * @return void.
 */
function mock_wc( $wc_countries ) {
	$wc            = new stdClass();
	$wc->countries = $wc_countries;
	\Spies\stub_function( 'WC' )->and_return( $wc );
}

/**
 * Mock function mocking the WooCommerce countries.
 *
 * @param array $eu_countries List of EU countries.
 *
 * @return array.
 */
function mock_wc_countries( array $eu_countries ) {
	$wc_countries = \Spies\mock_object();
	$wc_countries->add_method( 'get_european_union_countries' )->and_return( $eu_countries );

	return $wc_countries;
}

/**
 * Mock function mocking the box object.
 *
 * @param float $weight Weight of the box object.
 * @param float $length Length of the box object.
 * @param float $width Width of the box object.
 * @param float $height Height of the box object.
 *
 * @return object.
 */
function mock_box( $weight, $length, $width, $height ) {
	$box = \Spies\mock_object();
	$box->add_method( 'get_weight' )->and_return( $weight );
	$box->add_method( 'get_length' )->and_return( $length );
	$box->add_method( 'get_width' )->and_return( $width );
	$box->add_method( 'get_height' )->and_return( $height );

	return $box;
}

/**
 * Mock function for WP `apply_filters`.
 *
 * @param string $__ Hook ID.
 * @param string $value Value that can be hooked.
 * @param mixed ...$args Optional. Additional parameters to pass to the callback functions.
 *
 * @return mixed.
 */
function apply_filters( $__, $value, ...$args ) { // phpcs:ignore WooCommerce.Commenting.CommentHooks.MissingSinceComment --- Only for mocking
	return $value;
}

/**
 * Mock function for WP `__()`.
 *
 * @param string $value Translation text.
 * @param string $__ Translation ID.
 */
function __( $value, $__ ) { // phpcs:ignore Generic.CodeAnalysis.UnusedFunctionParameter.FoundAfterLastUsed --- Only for mocking
	return $value;
}
