<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for international standard online rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for international standard online rate.
 */
class RoyalMail_Online_International_Standard_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'US', 'international-standard', 'online' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$expectations = array(
			'letter'       => array(
				100 => array( 250, 250, 250, 250, 250, 250 ),
			),
			'large-letter' => array(
				100 => array( 325, 325, 325, 420, 420, 420 ),
				250 => array( 475, 475, 475, 635, 755, 650 ),
				500 => array( 585, 585, 585, 885, 1090, 920 ),
				750 => array( 695, 695, 695, 1180, 1500, 1230 ),
			),
			'packet'       => array(
				100  => array( 555, 605, 645, 745, 850, 820 ),
				250  => array( 555, 605, 645, 900, 960, 985 ),
				500  => array( 735, 815, 890, 1310, 1480, 1580 ),
				750  => array( 860, 950, 1060, 1615, 1820, 1870 ),
				1000 => array( 990, 1075, 1230, 1915, 2180, 2260 ),
				1250 => array( 1035, 1230, 1360, 2135, 2510, 2615 ),
				1500 => array( 1035, 1230, 1570, 2330, 2850, 2895 ),
				2000 => array( 1200, 1400, 1875, 2450, 3035, 3025 ),
			),
		);

		foreach ( $expectations as $band => $expectation ) {
			$this->assertEquals( $expectation, $this->service->get_rate_bands( $band ) );
		}
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		$this->assertEquals( array(), $this->service->get_rate_boxes() );

		// Call get_quotes() will set boxes with default ones.
		$this->service->get_quotes( array(), 'per_item', 'US' );
		$expectation = array(
			'letter'        => array(
				'length' => 240,
				'width'  => 165,
				'height' => 5,
				'weight' => 100,
			),
			'large-letter'  => array(
				'length' => 353,
				'width'  => 250,
				'height' => 25,
				'weight' => 750,
			),
			'long-parcel'   => array(
				'length' => 600,
				'width'  => 150,
				'height' => 150,
				'weight' => 2000,
			),
			'square-parcel' => array(
				'length' => 300,
				'width'  => 300,
				'height' => 300,
				'weight' => 2000,
			),
			'parcel'        => array(
				'length' => 450,
				'width'  => 225,
				'height' => 225,
				'weight' => 2000,
			),
			'medium-parcel' => array(
				'length' => 610,
				'width'  => 460,
				'height' => 460,
				'weight' => 20000,
			),
		);
		$this->assertEquals( $expectation, $this->service->get_rate_boxes() );

		// RoyalMail international rate services allow user-defined boxes.
		$user_defined_boxes = array(
			array(
				'inner_length' => 10,
				'inner_width'  => 10,
				'inner_height' => 10,
				'box_weight'   => 100,
			),
			array(
				'inner_length' => 5,
				'inner_width'  => 5,
				'inner_height' => 5,
				'box_weight'   => 50,
			),
		);
		$this->set_user_defined_boxes( $user_defined_boxes, $this->service->get_rate_boxes() );
	}
}
