<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for international tracked signed online rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for international tracked signed online rate.
 */
class RoyalMail_Online_International_Tracked_Signed_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'US', 'international-tracked-signed', 'online' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$expectations = array(
			'letter'         => array(
				100 => array( 815, 815, 815, 815, 815, 815, 815, 815, 815, 815 ),
			),
			'large-letter'   => array(
				100 => array( 965, 965, 965, 1065, 965, 965, 965, 1065, 1080, 1075 ),
				250 => array( 1020, 1020, 1020, 1160, 1020, 1020, 1020, 1160, 1280, 1180 ),
				500 => array( 1100, 1100, 1100, 1360, 1100, 1100, 1100, 1360, 1560, 1395 ),
				750 => array( 1145, 1145, 1145, 1565, 1145, 1145, 1145, 1565, 1865, 1615 ),
			),
			'packet'         => array(
				100  => array( 1015, 1015, 1015, 1540, 1015, 970, 1095, 1540, 1330, 1280 ),
				250  => array( 1015, 1015, 1015, 1575, 1015, 970, 1095, 1575, 1365, 1440 ),
				500  => array( 1135, 1135, 1135, 1665, 1135, 1125, 1250, 1665, 1815, 1880 ),
				750  => array( 1240, 1240, 1240, 1905, 1240, 1225, 1365, 1905, 2095, 1880 ),
				1000 => array( 1335, 1335, 1335, 2170, 1335, 1315, 1490, 2170, 2410, 2045 ),
				1250 => array( 1390, 1390, 1390, 2370, 1390, 1345, 1645, 2370, 2705, 2390 ),
				1500 => array( 1400, 1400, 1400, 2550, 1400, 1370, 1755, 2550, 3005, 2665 ),
				2000 => array( 1415, 1415, 1415, 2655, 1415, 1415, 2135, 2655, 3210, 2715 ),
			),
			'printed-papers' => array(
				100  => array( 1015, 1015, 1015, 1540, 1015, 970, 1095, 1540, 1330, 1280 ),
				250  => array( 1015, 1015, 1015, 1575, 1015, 970, 1095, 1575, 1365, 1440 ),
				500  => array( 1135, 1135, 1135, 1665, 1135, 1125, 1250, 1665, 1815, 1880 ),
				750  => array( 1240, 1240, 1240, 1905, 1240, 1225, 1365, 1905, 2095, 1880 ),
				1000 => array( 1335, 1335, 1335, 2170, 1335, 1315, 1490, 2170, 2410, 2045 ),
				1250 => array( 1390, 1390, 1390, 2370, 1390, 1345, 1645, 2370, 2705, 2390 ),
				1500 => array( 1400, 1400, 1400, 2550, 1400, 1370, 1755, 2550, 3005, 2665 ),
				2000 => array( 1415, 1415, 1415, 2655, 1415, 1415, 2135, 2655, 3210, 2715 ),
			),
			'medium-parcel'  => array(
				100   => array( 1070, 1120, 1020, 2250, 1400, 1275, 1570, 2535, 2695, 2345 ),
				250   => array( 1070, 1120, 1020, 2250, 1400, 1275, 1570, 2535, 2695, 2345 ),
				500   => array( 1070, 1120, 1020, 2250, 1400, 1275, 1570, 2535, 2695, 2345 ),
				750   => array( 1070, 1120, 1020, 2250, 1400, 1275, 1570, 2535, 2695, 2345 ),
				1000  => array( 1070, 1120, 1020, 2250, 1400, 1275, 1570, 2535, 2695, 2345 ),
				1250  => array( 1070, 1170, 1020, 2340, 1460, 1580, 2070, 2825, 3440, 3215 ),
				1500  => array( 1070, 1170, 1020, 2340, 1460, 1580, 2070, 2825, 3440, 3215 ),
				2000  => array( 1070, 1170, 1020, 2340, 1460, 1580, 2070, 2825, 3440, 3215 ),
				3000  => array( 1090, 1260, 1180, 2745, 1665, 1830, 2575, 2925, 3810, 3340 ),
				4000  => array( 1160, 1300, 1270, 3070, 1715, 3295, 3255, 3815, 4230, 3920 ),
				5000  => array( 1290, 1385, 1395, 3395, 1770, 3595, 3835, 4745, 4670, 4500 ),
				7500  => array( 1480, 1440, 1765, 4180, 2315, 5340, 5360, 6405, 6270, 6100 ),
				10000 => array( 1605, 1675, 2130, 4910, 2815, 7310, 7320, 9780, 8600, 7600 ),
				15000 => array( 1815, 1995, 2865, 5240, 3810, 10160, 9915, 12350, 12100, 11070 ),
				20000 => array( 1919, 2560, 3600, 5440, 4755, 15710, 12215, 17000, 16700, 13300 ),
			),
		);

		foreach ( $expectations as $band => $expectation ) {
			$this->assertEquals( $expectation, $this->service->get_rate_bands( $band ) );
		}
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		$this->assertEquals( array(), $this->service->get_rate_boxes() );

		// Call get_quotes() will set boxes with default ones.
		$this->service->get_quotes( array(), 'per_item', 'US' );
		$expectation = array(
			'letter'        => array(
				'length' => 240,
				'width'  => 165,
				'height' => 5,
				'weight' => 100,
			),
			'large-letter'  => array(
				'length' => 353,
				'width'  => 250,
				'height' => 25,
				'weight' => 750,
			),
			'long-parcel'   => array(
				'length' => 600,
				'width'  => 150,
				'height' => 150,
				'weight' => 2000,
			),
			'square-parcel' => array(
				'length' => 300,
				'width'  => 300,
				'height' => 300,
				'weight' => 2000,
			),
			'parcel'        => array(
				'length' => 450,
				'width'  => 225,
				'height' => 225,
				'weight' => 2000,
			),
			'medium-parcel' => array(
				'length' => 610,
				'width'  => 460,
				'height' => 460,
				'weight' => 20000,
			),
		);
		$this->assertEquals( $expectation, $this->service->get_rate_boxes() );

		// RoyalMail international rate services allow user-defined boxes.
		$user_defined_boxes = array(
			array(
				'inner_length' => 10,
				'inner_width'  => 10,
				'inner_height' => 10,
				'box_weight'   => 100,
			),
			array(
				'inner_length' => 5,
				'inner_width'  => 5,
				'inner_height' => 5,
				'box_weight'   => 50,
			),
		);
		$this->set_user_defined_boxes( $user_defined_boxes, $this->service->get_rate_boxes() );
	}
}
