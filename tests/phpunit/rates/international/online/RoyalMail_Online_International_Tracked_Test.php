<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for international tracked online rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for international tracked online rate.
 */
class RoyalMail_Online_International_Tracked_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'US', 'international-tracked', 'online' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$expectations = array(
			'letter'         => array(
				100 => array( 790, 790, 790, 790, 790, 790, 790, 790, 790, 790 ),
			),
			'large-letter'   => array(
				100 => array( 950, 950, 950, 1055, 950, 950, 950, 1055, 1060, 1060 ),
				250 => array( 980, 980, 980, 1125, 980, 980, 980, 1125, 1245, 1170 ),
				500 => array( 1090, 1090, 1090, 1350, 1090, 1090, 1090, 1350, 1545, 1380 ),
				750 => array( 1135, 1135, 1135, 1555, 1135, 1135, 1135, 1555, 1855, 1600 ),
			),
			'packet'         => array(
				100  => array( 865, 800, 800, 1375, 865, 900, 985, 1120, 1235, 1170 ),
				250  => array( 865, 800, 800, 1375, 865, 900, 985, 1120, 1235, 1170 ),
				500  => array( 865, 920, 920, 1375, 985, 990, 1170, 1420, 1585, 1490 ),
				750  => array( 1010, 1010, 975, 1620, 1080, 1045, 1305, 1665, 1965, 1550 ),
				1000 => array( 1010, 1075, 995, 1930, 1150, 1110, 1405, 1930, 2310, 1720 ),
				1250 => array( 1010, 1095, 995, 2165, 1190, 1150, 1500, 2175, 2640, 2000 ),
				1500 => array( 1010, 1095, 995, 2335, 1190, 1150, 1680, 2465, 2915, 2000 ),
				2000 => array( 1010, 1095, 995, 2455, 1210, 1215, 1950, 2645, 3110, 2000 ),
			),
			'printed-papers' => array(
				100  => array( 865, 800, 800, 1375, 865, 900, 985, 1120, 1235, 1170 ),
				250  => array( 865, 800, 800, 1375, 865, 900, 985, 1120, 1235, 1170 ),
				500  => array( 865, 920, 920, 1375, 985, 990, 1170, 1420, 1585, 1490 ),
				750  => array( 1010, 1010, 975, 1620, 1080, 1045, 1305, 1665, 1965, 1550 ),
				1000 => array( 1010, 1075, 995, 1930, 1150, 1110, 1405, 1930, 2310, 1720 ),
				1250 => array( 1010, 1095, 995, 2165, 1190, 1150, 1500, 2175, 2640, 2000 ),
				1500 => array( 1010, 1095, 995, 2335, 1190, 1150, 1680, 2465, 2915, 2000 ),
				2000 => array( 1010, 1095, 995, 2455, 1210, 1215, 1950, 2645, 3110, 2000 ),
			),
		);

		foreach ( $expectations as $band => $expectation ) {
			$this->assertEquals( $expectation, $this->service->get_rate_bands( $band ) );
		}
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		$this->assertEquals( array(), $this->service->get_rate_boxes() );

		// Call get_quotes() will set boxes with default ones.
		$this->service->get_quotes( array(), 'per_item', 'US' );
		$expectation = array(
			'letter'        => array(
				'length' => 240,
				'width'  => 165,
				'height' => 5,
				'weight' => 100,
			),
			'large-letter'  => array(
				'length' => 353,
				'width'  => 250,
				'height' => 25,
				'weight' => 750,
			),
			'long-parcel'   => array(
				'length' => 600,
				'width'  => 150,
				'height' => 150,
				'weight' => 2000,
			),
			'square-parcel' => array(
				'length' => 300,
				'width'  => 300,
				'height' => 300,
				'weight' => 2000,
			),
			'parcel'        => array(
				'length' => 450,
				'width'  => 225,
				'height' => 225,
				'weight' => 2000,
			),
			'medium-parcel' => array(
				'length' => 610,
				'width'  => 460,
				'height' => 460,
				'weight' => 20000,
			),
		);
		$this->assertEquals( $expectation, $this->service->get_rate_boxes() );

		// RoyalMail international rate services allow user-defined boxes.
		$user_defined_boxes = array(
			array(
				'inner_length' => 10,
				'inner_width'  => 10,
				'inner_height' => 10,
				'box_weight'   => 100,
			),
			array(
				'inner_length' => 5,
				'inner_width'  => 5,
				'inner_height' => 5,
				'box_weight'   => 50,
			),
		);
		$this->set_user_defined_boxes( $user_defined_boxes, $this->service->get_rate_boxes() );
	}
}
