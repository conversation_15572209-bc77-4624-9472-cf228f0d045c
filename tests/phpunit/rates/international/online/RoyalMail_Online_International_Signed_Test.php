<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for international signed online rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for international signed online rate.
 */
class RoyalMail_Online_International_Signed_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'US', 'international-signed', 'online' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$expectations = array(
			'letter'       => array(
				100 => array( 815, 815, 815, 815, 815 ),
			),
			'large-letter' => array(
				100 => array( 965, 965, 965, 1065, 1080 ),
				250 => array( 1020, 1020, 1020, 1160, 1280 ),
				500 => array( 1100, 1100, 1100, 1360, 1560 ),
				750 => array( 1145, 1145, 1145, 1565, 1865 ),
			),
			'packet'       => array(
				100  => array( 1015, 970, 1095, 1540, 1330 ),
				250  => array( 1015, 970, 1095, 1575, 1365 ),
				500  => array( 1135, 1125, 1250, 1665, 1815 ),
				750  => array( 1240, 1225, 1365, 1905, 2095 ),
				1000 => array( 1335, 1315, 1490, 2170, 2410 ),
				1250 => array( 1390, 1345, 1645, 2370, 2705 ),
				1500 => array( 1400, 1370, 1755, 2550, 3005 ),
				2000 => array( 1415, 1415, 2135, 2655, 3210 ),
			),
		);

		foreach ( $expectations as $band => $expectation ) {
			$this->assertEquals( $expectation, $this->service->get_rate_bands( $band ) );
		}
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		$this->assertEquals( array(), $this->service->get_rate_boxes() );

		// Call get_quotes() will set boxes with default ones.
		$this->service->get_quotes( array(), 'per_item', 'AF' );
		$expectation = array(
			'letter'        => array(
				'length' => 240,
				'width'  => 165,
				'height' => 5,
				'weight' => 100,
			),
			'large-letter'  => array(
				'length' => 353,
				'width'  => 250,
				'height' => 25,
				'weight' => 750,
			),
			'long-parcel'   => array(
				'length' => 600,
				'width'  => 150,
				'height' => 150,
				'weight' => 2000,
			),
			'square-parcel' => array(
				'length' => 300,
				'width'  => 300,
				'height' => 300,
				'weight' => 2000,
			),
			'parcel'        => array(
				'length' => 450,
				'width'  => 225,
				'height' => 225,
				'weight' => 2000,
			),
			'medium-parcel' => array(
				'length' => 610,
				'width'  => 460,
				'height' => 460,
				'weight' => 20000,
			),
		);
		$this->assertEquals( $expectation, $this->service->get_rate_boxes() );

		// RoyalMail international rate services allow user-defined boxes.
		$user_defined_boxes = array(
			array(
				'inner_length' => 10,
				'inner_width'  => 10,
				'inner_height' => 10,
				'box_weight'   => 100,
			),
			array(
				'inner_length' => 5,
				'inner_width'  => 5,
				'inner_height' => 5,
				'box_weight'   => 50,
			),
		);
		$this->set_user_defined_boxes( $user_defined_boxes, $this->service->get_rate_boxes() );
	}
}
