<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for Parcelforce Global economy regular rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for Parcelforce Global economy regular rate.
 */
class RoyalMail_Regular_Parcelforce_Globaleconomy_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'US', 'parcelforce-globaleconomy' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );

		$this->assertEquals( 4620, $this->service->get_rate_bands( '7' )[500] );
		$this->assertEquals( 12360, $this->service->get_rate_bands( '7' )[30000] );

		$this->assertEquals( 5118, $this->service->get_rate_bands( '8' )[500] );
		$this->assertEquals( 13974, $this->service->get_rate_bands( '8' )[30000] );

		$this->assertEquals( 5742, $this->service->get_rate_bands( '9' )[500] );
		$this->assertEquals( 15354, $this->service->get_rate_bands( '9' )[30000] );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		// Parcelforce worldwide rate services don't have default boxes.
		$this->assertEquals( array(), $this->service->get_rate_boxes() );

		// Parcelforce rate services allow user-defined boxes.
		$user_defined_boxes = array(
			array(
				'inner_length' => 10,
				'inner_width'  => 10,
				'inner_height' => 10,
				'box_weight'   => 100,
			),
			array(
				'inner_length' => 5,
				'inner_width'  => 5,
				'inner_height' => 5,
				'box_weight'   => 50,
			),
		);
		$this->set_user_defined_boxes( $user_defined_boxes, $this->service->get_rate_boxes() );
	}
}
