<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for Parcelforce Global priority regular rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for Parcelforce Global priority regular rate.
 */
class RoyalMail_Regular_Parcelforce_Globalpriority_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'US', 'parcelforce-globalpriority' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );

		$this->assertEquals( 5700, $this->service->get_rate_bands( '9_NON_EU' )[500] );
		$this->assertEquals( 17322, $this->service->get_rate_bands( '9_NON_EU' )[30000] );

		$this->assertEquals( 5880, $this->service->get_rate_bands( '10' )[500] );
		$this->assertEquals( 26550, $this->service->get_rate_bands( '10' )[30000] );

		$this->assertEquals( 6840, $this->service->get_rate_bands( '11' )[500] );
		$this->assertEquals( 36174, $this->service->get_rate_bands( '11' )[30000] );

		$this->assertEquals( 7440, $this->service->get_rate_bands( '12' )[500] );
		$this->assertEquals( 49260, $this->service->get_rate_bands( '12' )[30000] );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		// Parcelforce worldwide rate services don't have default boxes.
		$this->assertEquals( array(), $this->service->get_rate_boxes() );

		// Parcelforce rate services allow user-defined boxes.
		$user_defined_boxes = array(
			array(
				'inner_length' => 10,
				'inner_width'  => 10,
				'inner_height' => 10,
				'box_weight'   => 100,
			),
			array(
				'inner_length' => 5,
				'inner_width'  => 5,
				'inner_height' => 5,
				'box_weight'   => 50,
			),
		);
		$this->set_user_defined_boxes( $user_defined_boxes, $this->service->get_rate_boxes() );
	}
}
