<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for international tracked regular rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for international tracked regular rate.
 */
class RoyalMail_Regular_International_Tracked_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'US', 'international-tracked' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$expectations = array(
			'letter'         => array(
				100 => array( 790, 790, 790, 790, 790, 790 ),
			),
			'large-letter'   => array(
				100 => array( 950, 950, 950, 1055, 1060, 1060 ),
				250 => array( 1050, 1050, 1050, 1215, 1335, 1240 ),
				500 => array( 1160, 1160, 1160, 1420, 1615, 1450 ),
				750 => array( 1205, 1205, 1205, 1625, 1925, 1670 ),
			),
			'packet'         => array(
				250  => array( 1205, 1240, 1325, 1525, 1670, 1720 ),
				500  => array( 1335, 1370, 1500, 1970, 2180, 2185 ),
				750  => array( 1435, 1475, 1595, 2240, 2485, 2470 ),
				1000 => array( 1500, 1545, 1670, 2530, 2810, 2765 ),
				1250 => array( 1550, 1590, 1755, 2745, 3120, 3185 ),
				1500 => array( 1550, 1590, 1845, 2900, 3395, 3185 ),
				2000 => array( 1550, 1745, 1920, 3020, 3580, 3185 ),
			),
			'printed-papers' => array(
				250  => array( 1205, 1240, 1325, 1525, 1670, 1720 ),
				500  => array( 1335, 1370, 1500, 1970, 2180, 2185 ),
				750  => array( 1435, 1475, 1595, 2240, 2485, 2470 ),
				1000 => array( 1500, 1545, 1670, 2530, 2810, 2765 ),
				1250 => array( 1550, 1590, 1755, 2745, 3120, 3185 ),
				1500 => array( 1550, 1590, 1845, 2900, 3395, 3185 ),
				2000 => array( 1550, 1745, 1920, 3020, 3580, 3185 ),
			),
		);

		foreach ( $expectations as $band => $expectation ) {
			$this->assertEquals( $expectation, $this->service->get_rate_bands( $band ) );
		}
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		$this->assertEquals( array(), $this->service->get_rate_boxes() );

		// Call get_quotes() will set boxes with default ones.
		$this->service->get_quotes( array(), 'per_item', 'US' );
		$expectation = array(
			'letter'        => array(
				'length' => 240,
				'width'  => 165,
				'height' => 5,
				'weight' => 100,
			),
			'large-letter'  => array(
				'length' => 353,
				'width'  => 250,
				'height' => 25,
				'weight' => 750,
			),
			'long-parcel'   => array(
				'length' => 600,
				'width'  => 150,
				'height' => 150,
				'weight' => 2000,
			),
			'square-parcel' => array(
				'length' => 300,
				'width'  => 300,
				'height' => 300,
				'weight' => 2000,
			),
			'parcel'        => array(
				'length' => 450,
				'width'  => 225,
				'height' => 225,
				'weight' => 2000,
			),
			'medium-parcel' => array(
				'length' => 610,
				'width'  => 460,
				'height' => 460,
				'weight' => 20000,
			),
		);
		$this->assertEquals( $expectation, $this->service->get_rate_boxes() );

		// RoyalMail international rate services allow user-defined boxes.
		$user_defined_boxes = array(
			array(
				'inner_length' => 10,
				'inner_width'  => 10,
				'inner_height' => 10,
				'box_weight'   => 100,
			),
			array(
				'inner_length' => 5,
				'inner_width'  => 5,
				'inner_height' => 5,
				'box_weight'   => 50,
			),
		);
		$this->set_user_defined_boxes( $user_defined_boxes, $this->service->get_rate_boxes() );
	}
}
