<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for international standard regular rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for international standard regular rate.
 */
class RoyalMail_Regular_International_Standard_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'US', 'international-standard' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$expectations = array(
			'letter'         => array(
				100 => array( 250, 250, 250, 250, 250, 250 ),
			),
			'large-letter'   => array(
				100 => array( 325, 325, 325, 420, 420, 420 ),
				250 => array( 545, 545, 545, 705, 825, 720 ),
				500 => array( 655, 655, 655, 955, 1160, 990 ),
				750 => array( 765, 765, 765, 1250, 1570, 1300 ),
			),
			'packet'         => array(
				100  => array( 830, 845, 925, 1100, 1245, 1250 ),
				250  => array( 830, 845, 925, 1250, 1350, 1425 ),
				500  => array( 1040, 1075, 1140, 1700, 1870, 2045 ),
				750  => array( 1170, 1210, 1295, 2000, 2210, 2350 ),
				1000 => array( 1305, 1335, 1445, 2310, 2570, 2750 ),
				1250 => array( 1415, 1470, 1575, 2575, 2900, 3130 ),
				1500 => array( 1415, 1470, 1700, 2820, 3240, 3430 ),
				2000 => array( 1580, 1635, 1850, 2955, 3425, 3570 ),
			),
			'printed-papers' => array(
				100  => array( 830, 845, 925, 1100, 1245, 1250 ),
				250  => array( 830, 845, 925, 1250, 1350, 1425 ),
				500  => array( 1040, 1075, 1140, 1700, 1870, 2045 ),
				750  => array( 1170, 1210, 1295, 2000, 2210, 2350 ),
				1000 => array( 1305, 1335, 1445, 2310, 2570, 2750 ),
				1250 => array( 1415, 1470, 1575, 2575, 2900, 3130 ),
				1500 => array( 1415, 1470, 1700, 2820, 3240, 3430 ),
				2000 => array( 1580, 1635, 1850, 2955, 3425, 3570 ),
			),
		);

		foreach ( $expectations as $band => $expectation ) {
			$this->assertEquals( $expectation, $this->service->get_rate_bands( $band ) );
		}
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		$this->assertEquals( array(), $this->service->get_rate_boxes() );

		// Call get_quotes() will set boxes with default ones.
		$this->service->get_quotes( array(), 'per_item', 'US' );
		$expectation = array(
			'letter'        => array(
				'length' => 240,
				'width'  => 165,
				'height' => 5,
				'weight' => 100,
			),
			'large-letter'  => array(
				'length' => 353,
				'width'  => 250,
				'height' => 25,
				'weight' => 750,
			),
			'long-parcel'   => array(
				'length' => 600,
				'width'  => 150,
				'height' => 150,
				'weight' => 2000,
			),
			'square-parcel' => array(
				'length' => 300,
				'width'  => 300,
				'height' => 300,
				'weight' => 2000,
			),
			'parcel'        => array(
				'length' => 450,
				'width'  => 225,
				'height' => 225,
				'weight' => 2000,
			),
			'medium-parcel' => array(
				'length' => 610,
				'width'  => 460,
				'height' => 460,
				'weight' => 20000,
			),
		);
		$this->assertEquals( $expectation, $this->service->get_rate_boxes() );

		// RoyalMail international rate services allow user-defined boxes.
		$user_defined_boxes = array(
			array(
				'inner_length' => 10,
				'inner_width'  => 10,
				'inner_height' => 10,
				'box_weight'   => 100,
			),
			array(
				'inner_length' => 5,
				'inner_width'  => 5,
				'inner_height' => 5,
				'box_weight'   => 50,
			),
		);
		$this->set_user_defined_boxes( $user_defined_boxes, $this->service->get_rate_boxes() );
	}
}
