<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for international signed regular rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for international signed regular rate.
 */
class RoyalMail_Regular_International_Signed_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'US', 'international-signed' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$expectations = array(
			'letter'         => array(
				100 => array( 815, 815, 815, 815, 815 ),
			),
			'large-letter'   => array(
				100 => array( 965, 965, 965, 1065, 1080 ),
				250 => array( 1090, 1090, 1090, 1230, 1350 ),
				500 => array( 1170, 1170, 1170, 1430, 1630 ),
				750 => array( 1215, 1215, 1215, 1635, 1935 ),
			),
			'packet'         => array(
				100  => array( 1375, 1390, 1530, 1770, 1900 ),
				250  => array( 1375, 1390, 1530, 1810, 1935 ),
				500  => array( 1520, 1560, 1700, 2240, 2430 ),
				750  => array( 1635, 1670, 1825, 2500, 3740 ),
				1000 => array( 1740, 1770, 1955, 2795, 3085 ),
				1250 => array( 1800, 1805, 2025, 3015, 3355 ),
				1500 => array( 1810, 1830, 2090, 3170, 3630 ),
				2000 => array( 1825, 1880, 2140, 3220, 3745 ),
			),
			'printed-papers' => array(
				100  => array( 1375, 1390, 1530, 1770, 1900 ),
				250  => array( 1375, 1390, 1530, 1810, 1935 ),
				500  => array( 1520, 1560, 1700, 2240, 2430 ),
				750  => array( 1635, 1670, 1825, 2500, 3740 ),
				1000 => array( 1740, 1770, 1955, 2795, 3085 ),
				1250 => array( 1800, 1805, 2025, 3015, 3355 ),
				1500 => array( 1810, 1830, 2090, 3170, 3630 ),
				2000 => array( 1825, 1880, 2140, 3220, 3745 ),
			),
		);

		foreach ( $expectations as $band => $expectation ) {
			$this->assertEquals( $expectation, $this->service->get_rate_bands( $band ) );
		}
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		$this->assertEquals( array(), $this->service->get_rate_boxes() );

		// Call get_quotes() will set boxes with default ones.
		$this->service->get_quotes( array(), 'per_item', 'AF' );
		$expectation = array(
			'letter'        => array(
				'length' => 240,
				'width'  => 165,
				'height' => 5,
				'weight' => 100,
			),
			'large-letter'  => array(
				'length' => 353,
				'width'  => 250,
				'height' => 25,
				'weight' => 750,
			),
			'long-parcel'   => array(
				'length' => 600,
				'width'  => 150,
				'height' => 150,
				'weight' => 2000,
			),
			'square-parcel' => array(
				'length' => 300,
				'width'  => 300,
				'height' => 300,
				'weight' => 2000,
			),
			'parcel'        => array(
				'length' => 450,
				'width'  => 225,
				'height' => 225,
				'weight' => 2000,
			),
			'medium-parcel' => array(
				'length' => 610,
				'width'  => 460,
				'height' => 460,
				'weight' => 20000,
			),
		);
		$this->assertEquals( $expectation, $this->service->get_rate_boxes() );

		// RoyalMail international rate services allow user-defined boxes.
		$user_defined_boxes = array(
			array(
				'inner_length' => 10,
				'inner_width'  => 10,
				'inner_height' => 10,
				'box_weight'   => 100,
			),
			array(
				'inner_length' => 5,
				'inner_width'  => 5,
				'inner_height' => 5,
				'box_weight'   => 50,
			),
		);
		$this->set_user_defined_boxes( $user_defined_boxes, $this->service->get_rate_boxes() );
	}
}
