<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for Parcelforce Global Express regular rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for Parcelforce Global Express regular rate.
 */
class RoyalMail_Regular_Parcelforce_Globalexpress_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'US', 'parcelforce-globalexpress' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );

		$this->assertEquals( 6000, $this->service->get_rate_bands( '4' )[500] );
		$this->assertEquals( 22338, $this->service->get_rate_bands( '4' )[30000] );

		$this->assertEquals( 5460, $this->service->get_rate_bands( '5' )[500] );
		$this->assertEquals( 18498, $this->service->get_rate_bands( '5' )[30000] );

		$this->assertEquals( 5112, $this->service->get_rate_bands( '6' )[500] );
		$this->assertEquals( 17880, $this->service->get_rate_bands( '6' )[30000] );

		$this->assertEquals( 5160, $this->service->get_rate_bands( '7' )[500] );
		$this->assertEquals( 19434, $this->service->get_rate_bands( '7' )[30000] );

		$this->assertEquals( 5580, $this->service->get_rate_bands( '8' )[500] );
		$this->assertEquals( 24138, $this->service->get_rate_bands( '8' )[30000] );

		$this->assertEquals( 5988, $this->service->get_rate_bands( '9' )[500] );
		$this->assertEquals( 31278, $this->service->get_rate_bands( '9' )[30000] );

		$this->assertEquals( 5988, $this->service->get_rate_bands( '9_NON_EU' )[500] );
		$this->assertEquals( 31278, $this->service->get_rate_bands( '9_NON_EU' )[30000] );

		$this->assertEquals( 6180, $this->service->get_rate_bands( '10' )[500] );
		$this->assertEquals( 29430, $this->service->get_rate_bands( '10' )[30000] );

		$this->assertEquals( 7410, $this->service->get_rate_bands( '11' )[500] );
		$this->assertEquals( 43284, $this->service->get_rate_bands( '11' )[30000] );

		$this->assertEquals( 8400, $this->service->get_rate_bands( '12' )[500] );
		$this->assertEquals( 54552, $this->service->get_rate_bands( '12' )[30000] );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		// Parcelforce worldwide rate services don't have default boxes.
		$this->assertEquals( array(), $this->service->get_rate_boxes() );

		// Parcelforce rate services allow user-defined boxes.
		$user_defined_boxes = array(
			array(
				'inner_length' => 10,
				'inner_width'  => 10,
				'inner_height' => 10,
				'box_weight'   => 100,
			),
			array(
				'inner_length' => 5,
				'inner_width'  => 5,
				'inner_height' => 5,
				'box_weight'   => 50,
			),
		);
		$this->set_user_defined_boxes( $user_defined_boxes, $this->service->get_rate_boxes() );
	}
}
