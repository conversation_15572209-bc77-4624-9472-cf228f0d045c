<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for Parcelforce Ireland Express regular rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for Parcelforce Ireland Express regular rate.
 */
class RoyalMail_Regular_Parcelforce_Irelandexpress_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'US', 'parcelforce-irelandexpress' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );

		$this->assertEquals( 2754, $this->service->get_rate_bands( '5' )[500] );
		$this->assertEquals( 8154, $this->service->get_rate_bands( '5' )[30000] );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		// Parcelforce worldwide rate services don't have default boxes.
		$this->assertEquals( array(), $this->service->get_rate_boxes() );

		// Parcelforce rate services allow user-defined boxes.
		$user_defined_boxes = array(
			array(
				'inner_length' => 10,
				'inner_width'  => 10,
				'inner_height' => 10,
				'box_weight'   => 100,
			),
			array(
				'inner_length' => 5,
				'inner_width'  => 5,
				'inner_height' => 5,
				'box_weight'   => 50,
			),
		);
		$this->set_user_defined_boxes( $user_defined_boxes, $this->service->get_rate_boxes() );
	}
}
