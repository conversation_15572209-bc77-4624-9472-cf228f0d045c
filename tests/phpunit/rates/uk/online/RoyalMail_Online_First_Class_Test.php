<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for first class online rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for first class online rate.
 */
class RoyalMail_Online_First_Class_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'GB', 'first-class', 'online' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$expectations = array(
			'letter'              => array(
				100 => 135,
			),
			'large-letter'        => array(
				100 => 210,
				250 => 270,
				500 => 330,
				750 => 330,
			),
			'small-parcel-wide'   => array(
				1000 => 399,
				2000 => 399,
			),
			'small-parcel-deep'   => array(
				1000 => 399,
				2000 => 399,
			),
			'small-parcel-bigger' => array(
				1000 => 399,
				2000 => 399,
			),
			'medium-parcel'       => array(
				1000  => 549,
				2000  => 549,
				5000  => 719,
				10000 => 719,
				20000 => 1129,
			),
		);

		foreach ( $expectations as $band => $expectation ) {
			$this->assertEquals( $expectation, $this->service->get_rate_bands( $band ) );
		}
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		$expectation = array(
			'letter'              => array(
				'length' => 240,
				'width'  => 165,
				'height' => 5,
				'weight' => 100,
			),
			'large-letter'        => array(
				'length' => 353,
				'width'  => 250,
				'height' => 25,
				'weight' => 750,
			),
			'small-parcel-wide'   => array(
				'length' => 450,
				'width'  => 350,
				'height' => 80,
				'weight' => 2000,
			),
			'small-parcel-deep'   => array(
				'length' => 350,
				'width'  => 250,
				'height' => 160,
				'weight' => 2000,
			),
			'small-parcel-bigger' => array(
				'length' => 450,
				'width'  => 350,
				'height' => 160,
				'weight' => 2000,
			),
			'medium-parcel'       => array(
				'length' => 610,
				'width'  => 460,
				'height' => 460,
				'weight' => 20000,
			),
		);

		$this->assertEquals( $expectation, $this->service->get_rate_boxes() );
	}
}
