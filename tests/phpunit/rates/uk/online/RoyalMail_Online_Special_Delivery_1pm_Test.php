<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for special delivery 1pm online rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for special delivery 1pm online rate.
 */
class RoyalMail_Online_Special_Delivery_1pm_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'GB', 'special-delivery-1pm', 'online' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$expectations = array(
			750  => array(
				100   => 735,
				500   => 835,
				1000  => 935,
				2000  => 1215,
				10000 => 1655,
				20000 => 2055,
			),
			1000 => array(
				100   => 1035,
				500   => 1135,
				1000  => 1235,
				2000  => 1515,
				10000 => 1955,
				20000 => 2355,
			),
			2500 => array(
				100   => 1735,
				500   => 1835,
				1000  => 1935,
				2000  => 2215,
				10000 => 2655,
				20000 => 3055,
			),
		);

		foreach ( $expectations as $band => $expectation ) {
			$this->assertEquals( $expectation, $this->service->get_rate_bands( $band ) );
		}
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		$expectation = array(
			'packet' => array(
				'length' => 610,   // Max length.
				'width'  => 460,   // Max width.
				'height' => 460,   // Max height.
				'weight' => 20000, // Max weight.
			),
		);

		$this->assertEquals( $expectation, $this->service->get_rate_boxes() );
	}
}
