<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for second class online rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for second class online rate.
 */
class RoyalMail_Online_Second_Class_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'GB', 'second-class', 'online' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$expectations = array(
			'letter'              => array(
				100 => 85,
			),
			'large-letter'        => array(
				100 => 155,
				250 => 190,
				500 => 230,
				750 => 250,
			),
			'small-parcel-wide'   => array(
				1000 => 319,
				2000 => 319,
			),
			'small-parcel-deep'   => array(
				1000 => 319,
				2000 => 319,
			),
			'small-parcel-bigger' => array(
				1000 => 319,
				2000 => 319,
			),
			'medium-parcel'       => array(
				1000  => 469,
				2000  => 469,
				5000  => 619,
				10000 => 619,
				20000 => 979,
			),
		);

		foreach ( $expectations as $band => $expectation ) {
			$this->assertEquals( $expectation, $this->service->get_rate_bands( $band ) );
		}
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		$expectation = array(
			'letter'              => array(
				'length' => 240,
				'width'  => 165,
				'height' => 5,
				'weight' => 100,
			),
			'large-letter'        => array(
				'length' => 353,
				'width'  => 250,
				'height' => 25,
				'weight' => 750,
			),
			'small-parcel-wide'   => array(
				'length' => 450,
				'width'  => 350,
				'height' => 80,
				'weight' => 2000,
			),
			'small-parcel-deep'   => array(
				'length' => 350,
				'width'  => 250,
				'height' => 160,
				'weight' => 2000,
			),
			'small-parcel-bigger' => array(
				'length' => 450,
				'width'  => 350,
				'height' => 160,
				'weight' => 2000,
			),
			'medium-parcel'       => array(
				'length' => 610,
				'width'  => 460,
				'height' => 460,
				'weight' => 20000,
			),
		);

		$this->assertEquals( $expectation, $this->service->get_rate_boxes() );
	}
}
