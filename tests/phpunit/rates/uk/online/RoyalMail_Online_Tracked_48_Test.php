<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for tracked 48 online rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for tracked 48 online rate.
 */
class RoyalMail_Online_Tracked_48_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'GB', 'tracked-48', 'online' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$expectations = array(
			'large-letter'        => array(
				750 => 270,
			),
			'small-parcel-wide'   => array(
				2000 => 339,
			),
			'small-parcel-deep'   => array(
				2000 => 339,
			),
			'small-parcel-bigger' => array(
				2000 => 339,
			),
			'medium-parcel'       => array(
				2000  => 509,
				10000 => 659,
				20000 => 1019,
			),
			'tube'                => array(
				2000  => 509,
				10000 => 659,
				20000 => 1019,
			),
		);

		foreach ( $expectations as $band => $expectation ) {
			$this->assertEquals( $expectation, $this->service->get_rate_bands( $band ) );
		}
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		$expectation = array(
			'large-letter'        => array(
				'length' => 353,
				'width'  => 250,
				'height' => 25,
				'weight' => 750,
			),
			'small-parcel-wide'   => array(
				'length' => 450,
				'width'  => 350,
				'height' => 80,
				'weight' => 2000,
			),
			'small-parcel-deep'   => array(
				'length' => 350,
				'width'  => 250,
				'height' => 160,
				'weight' => 2000,
			),
			'small-parcel-bigger' => array(
				'length' => 450,
				'width'  => 350,
				'height' => 160,
				'weight' => 2000,
			),
			'medium-parcel'       => array(
				'length' => 610,
				'width'  => 460,
				'height' => 460,
				'weight' => 20000,
			),
			'tube'                => array(
				'length' => 900,
				'width'  => 70,
				'height' => 70,
				'weight' => 2000,
			),
		);

		$this->assertEquals( $expectation, $this->service->get_rate_boxes() );
	}
}
