<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for special delivery 9am regular rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for special delivery 9am regular rate.
 */
class RoyalMail_Regular_Special_Delivery_9am_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'GB', 'special-delivery-9am' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$expectations = array(
			50   => array(
				100  => 2995,
				500  => 3495,
				1000 => 3995,
				2000 => 4995,
			),
			1000 => array(
				100  => 3695,
				500  => 4195,
				1000 => 4695,
				2000 => 5695,
			),
			2500 => array(
				100  => 4495,
				500  => 4995,
				1000 => 5495,
				2000 => 6495,
			),
		);

		foreach ( $expectations as $band => $expectation ) {
			$this->assertEquals( $expectation, $this->service->get_rate_bands( $band ) );
		}
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		$expectation = array(
			'packet' => array(
				'length' => 610,
				'width'  => 460,
				'height' => 460,
				'weight' => 2000,
			),
		);

		$this->assertEquals( $expectation, $this->service->get_rate_boxes() );
	}
}
