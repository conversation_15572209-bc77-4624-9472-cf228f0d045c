<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for Parcelforce Express 9 regular rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for Parcelforce Express 9 regular rate.
 */
class RoyalMail_Regular_Parcelforce_Express_9_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'GB', 'parcelforce-express-9' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$expectations = array(
			200 => array(
				5000  => 5745,
				10000 => 6545,
				20000 => 7395,
				30000 => 8795,
			),
		);

		foreach ( $expectations as $band => $expectation ) {
			$this->assertEquals( $expectation, $this->service->get_rate_bands( $band ) );
		}
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		$expectation = array(
			'packet' => array(
				'length' => 1500,
				'width'  => 750,
				'height' => 750,
				'weight' => 30000,
			),
		);

		$this->assertEquals( $expectation, $this->service->get_rate_boxes() );
	}
}
