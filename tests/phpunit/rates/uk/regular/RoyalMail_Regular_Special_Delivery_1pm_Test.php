<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for special delivery 1pm regular rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for special delivery 1pm regular rate.
 */
class RoyalMail_Regular_Special_Delivery_1pm_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'GB', 'special-delivery-1pm' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$expectations = array(
			750  => array(
				100   => 795,
				500   => 895,
				1000  => 995,
				2000  => 1275,
				10000 => 1775,
				20000 => 2175,
			),
			1000 => array(
				100   => 1095,
				500   => 1195,
				1000  => 1295,
				2000  => 1575,
				10000 => 2075,
				20000 => 2475,
			),
			2500 => array(
				100   => 1795,
				500   => 1895,
				1000  => 1995,
				2000  => 2275,
				10000 => 2775,
				20000 => 3175,
			),
		);

		foreach ( $expectations as $band => $expectation ) {
			$this->assertEquals( $expectation, $this->service->get_rate_bands( $band ) );
		}
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		$expectation = array(
			'packet' => array(
				'length' => 610,
				'width'  => 460,
				'height' => 460,
				'weight' => 20000,
			),
		);

		$this->assertEquals( $expectation, $this->service->get_rate_boxes() );
	}
}
