<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for tracked 24 regular rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for tracked 24 regular rate.
 */
class RoyalMail_Regular_Tracked_24_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'GB', 'tracked-24' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$expectations = array(
			'large-letter'        => array(
				750 => 350,
			),
			'small-parcel-wide'   => array(
				2000 => 479,
			),
			'small-parcel-deep'   => array(
				2000 => 479,
			),
			'small-parcel-bigger' => array(
				2000 => 479,
			),
			'medium-parcel'       => array(
				2000  => 709,
				10000 => 879,
				20000 => 1289,
			),
			'tube'                => array(
				2000  => 709,
				10000 => 879,
				20000 => 1289,
			),
		);

		foreach ( $expectations as $band => $expectation ) {
			$this->assertEquals( $expectation, $this->service->get_rate_bands( $band ) );
		}
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		$expectation = array(
			'large-letter'        => array(
				'length' => 353,
				'width'  => 250,
				'height' => 25,
				'weight' => 750,
			),
			'small-parcel-wide'   => array(
				'length' => 450,
				'width'  => 350,
				'height' => 80,
				'weight' => 2000,
			),
			'small-parcel-deep'   => array(
				'length' => 350,
				'width'  => 250,
				'height' => 160,
				'weight' => 2000,
			),
			'small-parcel-bigger' => array(
				'length' => 450,
				'width'  => 350,
				'height' => 160,
				'weight' => 2000,
			),
			'medium-parcel'       => array(
				'length' => 610,
				'width'  => 460,
				'height' => 460,
				'weight' => 20000,
			),
			'tube'                => array(
				'length' => 900,
				'width'  => 70,
				'height' => 70,
				'weight' => 2000,
			),
		);

		$this->assertEquals( $expectation, $this->service->get_rate_boxes() );
	}
}
