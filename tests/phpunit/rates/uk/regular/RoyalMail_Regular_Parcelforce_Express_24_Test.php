<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for Parcelforce Express 24 regular rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for Parcelforce Express 24 regular rate.
 */
class RoyalMail_Regular_Parcelforce_Express_24_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();

		$this->service = $this->get_service_instance( 'GB', 'parcelforce-express-24' );
	}

	/**
	 * Test for getting the rate bands.
	 */
	public function test_get_rate_bands() {
		$expectations = array(
			150 => array(
				5000  => 1345,
				10000 => 1645,
				20000 => 1995,
				30000 => 2395,
			),
		);

		foreach ( $expectations as $band => $expectation ) {
			$this->assertEquals( $expectation, $this->service->get_rate_bands( $band ) );
		}
		$this->assertEquals( array(), $this->service->get_rate_bands( 'does not exist' ) );
	}

	/**
	 * Test for getting the rate for certain boxes.
	 */
	public function test_get_rate_boxes() {
		$expectation = array(
			'packet' => array(
				'length' => 1500,
				'width'  => 750,
				'height' => 750,
				'weight' => 30000,
			),
		);

		$this->assertEquals( $expectation, $this->service->get_rate_boxes() );
	}
}
