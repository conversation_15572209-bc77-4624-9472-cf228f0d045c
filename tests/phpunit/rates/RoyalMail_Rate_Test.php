<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for royalmail rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for royalmail rate.
 */
class RoyalMail_Rate_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();
		\Spies\stub_function( 'plugin_dir_path' )->that_returns( '' );
		\Spies\stub_function( 'untrailingslashit' )->that_returns( '.' );
	}

	/**
	 * Test the service instances.
	 */
	public function test_service_instances() {
		$this->verify_service_instances(
			new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( 'GB' ), 'per_item' )
		);

		$this->verify_service_instances(
			new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( 'US' ), 'per_item' )
		);
	}

	/**
	 * Verifying the service instances.
	 *
	 * @param WC_Shipping_Royalmail_Rates $rates Royalmail rates object.
	 */
	protected function verify_service_instances( WC_Shipping_Royalmail_Rates $rates ) {
		foreach ( $rates->get_services() as $service ) {
			require_once $rates->get_service_class_path( $service );
			$classname = $rates->get_service_class_name( $service );

			$this->assertTrue( class_exists( $classname ) );
			$service_instance = new $classname();
			$this->assertTrue( is_a( $service_instance, 'RoyalMail_Rate' ) );
		}
	}

	/**
	 * Test for getting the zone.
	 */
	public function test_get_zone() {
		$service = $this->get_service_instance( 'GB', 'first-class' );

		$this->assertEquals( 'UK', $service->get_zone( 'GB' ) );

		mock_wc( mock_wc_countries( array( 'DE' ) ) );
		$this->assertEquals( 'EUR_1', $service->get_zone( 'DE' ) );

		$this->assertEquals( 'EUR_3', $service->get_zone( 'AL' ) );
		$this->assertEquals( 'EUR_3', $service->get_zone( 'NO' ) );

		$this->assertEquals( '2', $service->get_zone( 'AU' ) );
		$this->assertEquals( '2', $service->get_zone( 'NZ' ) );
		$this->assertEquals( '2', $service->get_zone( 'SG' ) );

		$this->assertEquals( '1', $service->get_zone( 'ID' ) );
		$this->assertEquals( '1', $service->get_zone( 'CN' ) );
	}

	/**
	 * Test if the box is letter.
	 */
	public function test_box_is_letter() {
		$service = $this->get_service_instance( 'GB', 'first-class' );

		// Not a letter specs.
		$box = mock_box( 101, 0, 0, 0 );
		$this->assertFalse( $service->box_is_letter( $box ) );
		$box = mock_box( 100, 241, 0, 0 );
		$this->assertFalse( $service->box_is_letter( $box ) );
		$box = mock_box( 100, 240, 166, 0 );
		$this->assertFalse( $service->box_is_letter( $box ) );
		$box = mock_box( 100, 240, 165, 6 );
		$this->assertFalse( $service->box_is_letter( $box ) );

		// Letter specs.
		$box = mock_box( 0, 0, 0, 0 );
		$this->assertTrue( $service->box_is_letter( $box ) );
		$box = mock_box( 100, 240, 165, 5 );
		$this->assertTrue( $service->box_is_letter( $box ) );
	}

	/**
	 * Test if the box is large letter.
	 */
	public function test_box_is_large_letter() {
		$service = $this->get_service_instance( 'GB', 'first-class' );

		// Not a large letter specs.
		$box = mock_box( 751, 0, 0, 0 );
		$this->assertFalse( $service->box_is_large_letter( $box ) );
		$box = mock_box( 750, 354, 0, 0 );
		$this->assertFalse( $service->box_is_large_letter( $box ) );
		$box = mock_box( 750, 353, 251, 0 );
		$this->assertFalse( $service->box_is_large_letter( $box ) );
		$box = mock_box( 750, 353, 250, 26 );
		$this->assertFalse( $service->box_is_large_letter( $box ) );

		// Large letter specs.
		$box = mock_box( 0, 0, 0, 0 );
		$this->assertTrue( $service->box_is_large_letter( $box ) );
		$box = mock_box( 750, 353, 250, 25 );
		$this->assertTrue( $service->box_is_large_letter( $box ) );
	}
}
