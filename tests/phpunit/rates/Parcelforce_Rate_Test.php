<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Test file for parcelforce rate.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Test class for parcelforce rate.
 */
class Parcelforce_Rate_Test extends Royalmail_TestCase {
	/**
	 * Setting up the test.
	 */
	protected function setUp(): void {
		parent::setUp();
		\Spies\stub_function( 'plugin_dir_path' )->that_returns( '' );
		\Spies\stub_function( 'untrailingslashit' )->that_returns( '.' );
	}

	/**
	 * Test service instances.
	 */
	public function test_service_instances() {
		$this->verify_service_instances(
			new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( 'US' ), 'per_item' )
		);
	}

	/**
	 * Verifying the service instances.
	 *
	 * @param WC_Shipping_Royalmail_Rates $rates Royalmail rates.
	 */
	protected function verify_service_instances( WC_Shipping_Royalmail_Rates $rates ) {
		$parcelforce_children = array(
			'parcelforce-irelandexpress',
			'parcelforce-globaleconomy',
			'parcelforce-globalexpress',
			'parcelforce-globalpriority',
			'parcelforce-globalvalue',
		);

		foreach ( $rates->get_services() as $service ) {
			require_once $rates->get_service_class_path( $service );
			$classname = $rates->get_service_class_name( $service );

			$this->assertTrue( class_exists( $classname ) );
			$service_instance = new $classname();
			$this->assertTrue( is_a( $service_instance, 'RoyalMail_Rate' ) );

			if ( in_array( $service, $parcelforce_children, true ) ) {
				$this->assertTrue( is_a( $service_instance, 'Parcelforce_Rate' ) );
			}
		}
	}

	/**
	 * Test for getting the zone.
	 */
	public function test_get_zone() {
		$service = $this->get_service_instance( 'US', 'parcelforce-globalexpress' );
		mock_wc( mock_wc_countries( array( 'AT' ) ) );

		$this->assertEquals( '4', $service->get_zone( 'JE' ) );
		$this->assertEquals( '4', $service->get_zone( 'GG' ) );
		$this->assertEquals( '4', $service->get_zone( 'IM' ) );

		$this->assertEquals( '12', $service->get_zone( 'IR' ) );

		$this->assertEquals( '6', $service->get_zone( 'BE' ) );
		$this->assertEquals( '6', $service->get_zone( 'NL' ) );
		$this->assertEquals( '6', $service->get_zone( 'LU' ) );

		$this->assertEquals( '7', $service->get_zone( 'FR' ) );
		$this->assertEquals( '7', $service->get_zone( 'DE' ) );
		$this->assertEquals( '7', $service->get_zone( 'DK' ) );

		$this->assertEquals( '8', $service->get_zone( 'IT' ) );
		$this->assertEquals( '8', $service->get_zone( 'ES' ) );
		$this->assertEquals( '8', $service->get_zone( 'PT' ) );
		$this->assertEquals( '8', $service->get_zone( 'GR' ) );

		$this->assertEquals( '9', $service->get_zone( 'AT' ) );

		$this->assertEquals( '10', $service->get_zone( 'US' ) );
		$this->assertEquals( '10', $service->get_zone( 'CA' ) );

		$this->assertEquals( '11', $service->get_zone( 'CN' ) );
		$this->assertEquals( '11', $service->get_zone( 'HK' ) );
		$this->assertEquals( '11', $service->get_zone( 'MO' ) );
		$this->assertEquals( '11', $service->get_zone( 'JP' ) );
	}

	/**
	 * Test for volumetric weight.
	 */
	public function test_get_volumetric_weight() {
		$service = $this->get_service_instance( 'US', 'parcelforce-globalexpress' );

		$this->assertEquals( 0, $service->get_volumetric_weight( 0, 0, 0 ) );
		$this->assertEquals( 2.7, $service->get_volumetric_weight( 30, 30, 15 ) );
		$this->assertEquals( 12, $service->get_volumetric_weight( 40, 30, 50 ) );
	}

	/**
	 * Test for additional compensation cost.
	 */
	public function test_get_additional_compensation_cost() {
		// irelandexpress.
		$service = $this->get_service_instance( 'US', 'parcelforce-irelandexpress' );
		$this->assertEquals( 0, $service->get_additional_compensation_cost( 50 ) );
		$this->assertEquals( 0, $service->get_additional_compensation_cost( 100 ) );
		$this->assertEquals( 0, $service->get_additional_compensation_cost( 200 ) );
		$this->assertEquals( 180, $service->get_additional_compensation_cost( 300 ) );
		$this->assertEquals( 630, $service->get_additional_compensation_cost( 350 ) );
		$this->assertEquals( 630, $service->get_additional_compensation_cost( 400 ) );
		$this->assertEquals( 1080, $service->get_additional_compensation_cost( 500 ) );
		$this->assertEquals( 1530, $service->get_additional_compensation_cost( 550 ) );
		$this->assertEquals( 10080, $service->get_additional_compensation_cost( 2500 ) );

		// globaleconomy doesn't have compensation.
		$service = $this->get_service_instance( 'US', 'parcelforce-globaleconomy' );
		$this->assertEquals( 0, $service->get_additional_compensation_cost( 2500 ) );

		// globalexpress.
		$service = $this->get_service_instance( 'US', 'parcelforce-globalexpress' );
		$this->assertEquals( 0, $service->get_additional_compensation_cost( 50 ) );
		$this->assertEquals( 0, $service->get_additional_compensation_cost( 100 ) );
		$this->assertEquals( 0, $service->get_additional_compensation_cost( 200 ) );
		$this->assertEquals( 180, $service->get_additional_compensation_cost( 300 ) );
		$this->assertEquals( 630, $service->get_additional_compensation_cost( 350 ) );
		$this->assertEquals( 630, $service->get_additional_compensation_cost( 400 ) );
		$this->assertEquals( 1080, $service->get_additional_compensation_cost( 500 ) );
		$this->assertEquals( 1530, $service->get_additional_compensation_cost( 550 ) );
		$this->assertEquals( 10080, $service->get_additional_compensation_cost( 2500 ) );

		// globalpriority.
		$service = $this->get_service_instance( 'US', 'parcelforce-globalpriority' );
		$this->assertEquals( 0, $service->get_additional_compensation_cost( 50 ) );
		$this->assertEquals( 0, $service->get_additional_compensation_cost( 100 ) );
		$this->assertEquals( 180, $service->get_additional_compensation_cost( 200 ) );
		$this->assertEquals( 630, $service->get_additional_compensation_cost( 300 ) );
		$this->assertEquals( 1080, $service->get_additional_compensation_cost( 350 ) );
		$this->assertEquals( 1080, $service->get_additional_compensation_cost( 400 ) );
		$this->assertEquals( 1530, $service->get_additional_compensation_cost( 500 ) );
		$this->assertEquals( 1980, $service->get_additional_compensation_cost( 550 ) );
		$this->assertEquals( 10530, $service->get_additional_compensation_cost( 2500 ) );

		// globalvalue.
		$service = $this->get_service_instance( 'US', 'parcelforce-globalvalue' );
		$this->assertEquals( 0, $service->get_additional_compensation_cost( 50 ) );
		$this->assertEquals( 0, $service->get_additional_compensation_cost( 100 ) );
		$this->assertEquals( 180, $service->get_additional_compensation_cost( 200 ) );
		$this->assertEquals( 630, $service->get_additional_compensation_cost( 300 ) );
		$this->assertEquals( 1080, $service->get_additional_compensation_cost( 350 ) );
		$this->assertEquals( 1080, $service->get_additional_compensation_cost( 400 ) );
		$this->assertEquals( 1530, $service->get_additional_compensation_cost( 500 ) );
		$this->assertEquals( 1980, $service->get_additional_compensation_cost( 550 ) );
		$this->assertEquals( 1980, $service->get_additional_compensation_cost( 2500 ) );
	}
}
