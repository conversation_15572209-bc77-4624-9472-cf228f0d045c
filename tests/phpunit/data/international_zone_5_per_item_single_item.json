[{"description": ["Test send letter to zone 7 that's within RoyalMail letter's requirement.", "Zone 7 includes France, Germany, and Denmark.", "Shipped items worth £50. Letter requirement:", "", "RoyalMail restrictions: max weight: 100g, max. dim (LxWxH): 240mm x 165mm x 5mm.", "Parceforce accepts parcels up to 1500mm in length, 3000mm", "length and girth combined, and max. of 30000g per item.", ""], "contents": [{"weight": 100, "length": 230, "width": 160, "height": 5, "price": 50, "quantity": 1}], "expected_quotes": {"international-tracked": "6.55", "international-standard": "1.60", "international-tracked-signed": "6.55", "international-economy": "2.50", "international-signed": "6.55", "parcelforce-globalexpress": "36.90", "parcelforce-globalpriority": "29.55", "parcelforce-globalvalue": "16.95"}}, {"description": ["Test send parcel to zone 7 that's within RoyalMail parcel's requirement.", "Zone 7 includes France, Germany, and Denmark.", "Shipped items worth £100. Parcel requirement:", "", "RoyalMail restrictions: max weight: 2000g. ( L + W + H ) <= 900mm, no single side > 600mm.", "Parceforce accepts parcels up to 1500mm in length, 3000mm", "length and girth combined, and max. of 30000g per item.", ""], "contents": [{"weight": 2000, "length": 450, "width": 200, "height": 200, "price": 100, "quantity": 1}], "expected_quotes": {"international-tracked": "20.88", "international-standard": "12.10", "international-tracked-signed": "14.90", "international-economy": "13.35", "international-signed": "14.90", "parcelforce-globalexpress": "45.75", "parcelforce-globalpriority": "34.35", "parcelforce-globalvalue": "18.45"}}, {"description": ["Test send parcel to zone 7 that's above RoyalMail parcel's requirement.", "Zone 7 includes France, Germany, and Denmark.", "Shipped items worth £1000.", "", "Parceforce accepts parcels up to 1500mm in length, 3000mm", "length and girth combined, and max. of 30000g per item.", ""], "contents": [{"weight": 2500, "length": 640, "width": 440, "height": 155, "price": 1000, "quantity": 1}], "expected_quotes": {"parcelforce-globalexpress": "76.95", "parcelforce-globalpriority": "67.60"}}, {"description": ["Test send parcel to zone 7 hat almost hits max weight (30000g).", "Volumetric weight (29840g) will be used instead of actual weight.", "Zone 7 includes France, Germany, and Denmark.", "Shipped item weight 29000g.", ""], "contents": [{"weight": 29000, "length": 1480, "width": 480, "height": 210, "price": 2500, "quantity": 1}], "expected_quotes": {"parcelforce-globalexpress": "245.95", "parcelforce-globalpriority": "172.10"}}, {"description": ["Test send parcel to zone 7 over the maximum weight (30000g).", "Zone 7 includes France, Germany, and Denmark.", "This should return empty.", ""], "contents": [{"weight": 31000, "length": 1000, "width": 500, "height": 100, "price": 2600, "quantity": 1}], "expected_quotes": {}}, {"description": ["Test send parcel to zone 7 that hits the limit of globalvalue compensation.", "Zone 7 includes France, Germany, and Denmark.", "Shipped items worth £500. This is the max. cover for globalvalue", ""], "contents": [{"weight": 10000, "length": 1000, "width": 500, "height": 100, "price": 500, "quantity": 1}], "expected_quotes": {"parcelforce-globalvalue": "60.30", "parcelforce-globalexpress": "101.45", "parcelforce-globalpriority": "70.10"}}, {"description": ["Test send parcel to zone 7 that hits the limit of compensation.", "Zone 7 includes France, Germany, and Denmark.", "Shipped items worth £2500. This is the max. cover for globalpriority", "and globalexpress. globalvalue won't show up in the result.", ""], "contents": [{"weight": 10000, "length": 1000, "width": 500, "height": 100, "price": 2500, "quantity": 1}], "expected_quotes": {"parcelforce-globalexpress": "176.45", "parcelforce-globalpriority": "145.10"}}, {"description": ["Test send parcel to zone 7 with compensation over the maximum (2500).", "Zone 7 includes France, Germany, and Denmark.", "Shipped items worth £2600. This should return empty.", ""], "contents": [{"weight": 10000, "length": 1000, "width": 500, "height": 100, "price": 2600, "quantity": 1}], "expected_quotes": {}}]