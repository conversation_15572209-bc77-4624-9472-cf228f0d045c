[{"description": ["Scenario zone 8-1", "", "Test send letter to zone 8 that's within RoyalMail letter's requirement.", "Zone 8 includes Italy, Spain, Portugal and Greece.", "Shipped items worth £50. Letter requirement:", "", "RoyalMail restrictions: max weight: 100g, max. dim (LxWxH): 240mm x 165mm x 5mm.", "Parceforce accepts parcels up to 1500mm in length, 3000mm", "length and girth combined, and max. of 30000g per item.", ""], "contents": [{"weight": 100, "length": 230, "width": 160, "height": 5, "price": 50, "quantity": 1}], "expected_quotes": {"international-tracked": "7.90", "international-tracked-signed": "8.15", "parcelforce-globalexpress": "46.50", "parcelforce-globaleconomy": "42.65"}}, {"description": ["Scenario zone 8-2", "", "Test send parcel to zone 8 that's within RoyalMail parcel's requirement.", "Zone 8 includes Italy, Spain, Portugal and Greece.", "Shipped items worth £100. Parcel requirement:", "", "RoyalMail restrictions: max weight: 2000g. ( L + W + H ) <= 900mm, no single side > 600mm.", "Parceforce accepts parcels up to 1500mm in length, 3000mm", "length and girth combined, and max. of 30000g per item.", ""], "contents": [{"weight": 2000, "length": 450, "width": 200, "height": 200, "price": 100, "quantity": 1}], "expected_quotes": {"parcelforce-globalexpress": "68.75", "parcelforce-globaleconomy": "58.95"}}, {"description": ["Scenario zone 8-3", "", "Test send parcel to zone 8 that's above RoyalMail parcel's requirement.", "Zone 8 includes Italy, Spain, Portugal and Greece.", "Shipped items worth £1000.", "", "Parceforce accepts parcels up to 1500mm in length, 3000mm", "length and girth combined, and max. of 30000g per item.", ""], "contents": [{"weight": 2500, "length": 640, "width": 440, "height": 155, "price": 1000, "quantity": 1}], "expected_quotes": {"parcelforce-globalexpress": "125.20", "parcelforce-globaleconomy": "76.35"}}, {"description": ["Scenario zone 8-4", "", "Test send parcel to zone 8 hat almost hits max weight (30000g).", "Volumetric weight (29840g) will be used instead of actual weight.", "Zone 8 includes Italy, Spain, Portugal and Greece.", "Shipped item weight 29000g.", ""], "contents": [{"weight": 29000, "length": 1480, "width": 480, "height": 210, "price": 2500, "quantity": 1}], "expected_quotes": {"parcelforce-globalexpress": "285.15", "parcelforce-globaleconomy": "116.45"}}, {"description": ["Scenario zone 8-5", "", "Test send parcel to zone 8 over the maximum weight (30000g).", "Zone 8 includes Italy, Spain, Portugal and Greece.", "This should return empty.", ""], "contents": [{"weight": 31000, "length": 1000, "width": 500, "height": 100, "price": 2600, "quantity": 1}], "expected_quotes": {}}, {"description": ["Scenario zone 8-6", "", "Test send parcel to zone 8 that hits the limit of globalvalue compensation.", "Zone 8 includes Italy, Spain, Portugal and Greece.", "Shipped items worth £500. This is the max. cover for globalvalue", ""], "contents": [{"weight": 10000, "length": 1000, "width": 500, "height": 100, "price": 500, "quantity": 1}], "expected_quotes": {"parcelforce-globalexpress": "112.15", "parcelforce-globaleconomy": "79.45"}}, {"description": ["Scenario zone 8-7", "", "Test send parcel to zone 8 that hits the limit of compensation.", "Zone 8 includes Italy, Spain, Portugal and Greece.", "Shipped items worth £2500. This is the max. cover for globalpriority", "and globalexpress. globalvalue won't show up in the result.", ""], "contents": [{"weight": 10000, "length": 1000, "width": 500, "height": 100, "price": 2500, "quantity": 1}], "expected_quotes": {"parcelforce-globalexpress": "187.15", "parcelforce-globaleconomy": "79.45"}}, {"description": ["Scenario zone 8-8", "", "Test send parcel to zone 8 with compensation over the maximum (2500).", "Zone 8 includes Italy, Spain, Portugal and Greece.", "Shipped items worth £2600. This should return empty.", ""], "contents": [{"weight": 10000, "length": 1000, "width": 500, "height": 100, "price": 2600, "quantity": 1}], "expected_quotes": {"parcelforce-globaleconomy": "79.45"}}]