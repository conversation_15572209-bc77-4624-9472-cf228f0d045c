[{"description": ["Test send letter within Channel Islands that's within letter's requirement.", "Shipped items worth £20. Letter requirement:", "", "Max weight: 100g, Max Dim (LxWxH): 240mm x 165mm x 5mm", ""], "contents": [{"weight": 100, "length": 230, "width": 160, "height": 5, "price": 20, "quantity": 1}], "expected_quotes": {"first-class": "1.35", "first-class-signed": "3.05", "second-class": "0.85", "second-class-signed": "2.55", "special-delivery-1pm": "7.95", "parcelforce-express-9": "47.88", "parcelforce-express-10": "22.88", "parcelforce-express-am": "14.54", "parcelforce-express-24": "11.21", "parcelforce-express-48": "10.79", "parcelforce-express-48-large": "39.96", "tracked-24": "3.50", "tracked-24-signed": "5.20", "tracked-48": "2.70", "tracked-48-signed": "4.40"}}, {"description": ["Test send letter within Channel Islands that's equal to upper bound of letter requirement.", "Shipped items worth £100. Letter requirement:", "", "Max length: 100g, Max Dim (LxWxH): 240mm x 165mm x 5mm", ""], "contents": [{"weight": 100, "length": 240, "width": 165, "height": 5, "price": 100, "quantity": 1}], "expected_quotes": {"special-delivery-1pm": "7.95", "parcelforce-express-9": "47.88", "parcelforce-express-10": "22.88", "parcelforce-express-am": "14.54", "parcelforce-express-24": "11.21", "parcelforce-express-48": "10.79", "parcelforce-express-48-large": "39.96", "tracked-24": "3.50", "tracked-24-signed": "5.20", "tracked-48": "2.70", "tracked-48-signed": "4.40"}}, {"description": ["Test send letter within Channel Islands that's within large letter's requirement.", "Shipped items worth £20. Large letter requirement:", "", "Max weight: 750g, Max Dim (LxWxH): 353mm x 250mm x 25mm", ""], "contents": [{"weight": 700, "length": 350, "width": 245, "height": 20, "price": 20, "quantity": 1}], "expected_quotes": {"first-class": "3.50", "first-class-signed": "5.20", "second-class": "2.70", "second-class-signed": "4.40", "special-delivery-1pm": "9.95", "parcelforce-express-9": "47.88", "parcelforce-express-10": "22.88", "parcelforce-express-am": "14.54", "parcelforce-express-24": "11.21", "parcelforce-express-48": "10.79", "parcelforce-express-48-large": "39.96", "tracked-24": "3.50", "tracked-24-signed": "5.20", "tracked-48": "2.70", "tracked-48-signed": "4.40"}}, {"description": ["Test send letter within Channel Islands that's equal to upper bound of large letter requirement.", "Shipped items worth £100. Large letter requirement:", "", "Max weight: 750g, Max Dim (LxWxH): 353mm x 250mm x 25mm", ""], "contents": [{"weight": 750, "length": 353, "width": 250, "height": 25, "price": 100, "quantity": 1}], "expected_quotes": {"special-delivery-1pm": "9.95", "parcelforce-express-9": "47.88", "parcelforce-express-10": "22.88", "parcelforce-express-am": "14.54", "parcelforce-express-24": "11.21", "parcelforce-express-48": "10.79", "parcelforce-express-48-large": "39.96", "tracked-24": "3.50", "tracked-24-signed": "5.20", "tracked-48": "2.70", "tracked-48-signed": "4.40"}}, {"description": ["Test send letter within Channel Islands that's within small parcel's requirement.", "Shipped items worth £50. Small parcel requirement:", "", "Max weight: 2000g, Max Dim (LxWxH): 450mm x 350mm x 160mm", ""], "contents": [{"weight": 1900, "length": 440, "width": 340, "height": 155, "price": 50, "quantity": 1}], "expected_quotes": {"second-class-signed": "5.09", "special-delivery-1pm": "12.75", "parcelforce-express-9": "47.88", "parcelforce-express-10": "22.88", "parcelforce-express-am": "14.54", "parcelforce-express-24": "11.21", "parcelforce-express-48": "10.79", "parcelforce-express-48-large": "39.96", "tracked-24": "4.79", "tracked-24-signed": "6.19", "tracked-48": "3.89", "tracked-48-signed": "5.29"}}, {"description": ["Test send letter within Channel Islands that's equal to upper bound of small parcel's requirement.", "Shipped items worth £100. Small parcel requirement:", "", "Max weight: 2000g, Max Dim (LxWxH): 450mm x 350mm x 160mm", ""], "contents": [{"weight": 2000, "length": 450, "width": 350, "height": 160, "price": 100, "quantity": 1}], "expected_quotes": {"special-delivery-1pm": "12.75", "parcelforce-express-9": "47.88", "parcelforce-express-10": "22.88", "parcelforce-express-am": "14.54", "parcelforce-express-24": "11.21", "parcelforce-express-48": "10.79", "parcelforce-express-48-large": "39.96", "tracked-24": "4.79", "tracked-24-signed": "6.19", "tracked-48": "3.89", "tracked-48-signed": "5.29"}}, {"description": ["Test send letter within Channel Islands that's within medium parcel's requirement.", "Shipped items worth £50. Medium parcel requirement:", "", "Max weight: 20000g, Max Dim (LxWxH): 610mm x 460mm x 460mm", ""], "contents": [{"weight": 15000, "length": 600, "width": 455, "height": 455, "price": 50, "quantity": 1}], "expected_quotes": {"second-class-signed": "12.39", "special-delivery-1pm": "21.75", "parcelforce-express-9": "61.63", "parcelforce-express-10": "28.29", "parcelforce-express-am": "19.96", "parcelforce-express-24": "16.63", "parcelforce-express-48": "14.96", "parcelforce-express-48-large": "56.63", "tracked-24": "12.89", "tracked-24-signed": "14.29", "tracked-48": "11.39", "tracked-48-signed": "12.79"}}, {"description": ["Test send letter within Channel Islands that's equal to upper bound of medium parcel's requirement.", "Shipped items worth £100. Medium parcel requirement:", "", "Max weight: 20000g, Max Dim (LxWxH): 610mm x 460mm x 460mm", ""], "contents": [{"weight": 20000, "length": 610, "width": 460, "height": 460, "price": 100, "quantity": 1}], "expected_quotes": {"special-delivery-1pm": "21.75", "parcelforce-express-9": "61.63", "parcelforce-express-10": "28.29", "parcelforce-express-am": "19.96", "parcelforce-express-24": "16.63", "parcelforce-express-48": "14.96", "parcelforce-express-48-large": "56.63", "tracked-24": "12.89", "tracked-24-signed": "14.29", "tracked-48": "11.39", "tracked-48-signed": "12.79"}}, {"description": ["Test send letter within Channel Islands that's within large parcel's requirement.", "Shipped items worth £50. Large parcel requirement:", "", "Max weight: 30000g, 2500mm max length and 5000mm length and girth combined.", "If the combined length + girth greater than 3000mm, only parcelforce-express-48", "will be returned", ""], "contents": [{"weight": 25000, "length": 2400, "width": 1200, "height": 1200, "price": 50, "quantity": 1}], "expected_quotes": {"parcelforce-express-48-large": "75.79"}}, {"description": ["Test send letter within Channel Islands that's equal to upper bound of large parcel's requirement.", "Shipped items worth £100. Large parcel requirement:", "", "Max weight: 30000g, 2500mm max length and 5000mm length and girth combined.", "If the combined length + girth greater than 3000mm, only parcelforce-express-48", "will be returned", ""], "contents": [{"weight": 30000, "length": 2500, "width": 1250, "height": 1250, "price": 100, "quantity": 1}], "expected_quotes": {"parcelforce-express-48-large": "75.79"}}]