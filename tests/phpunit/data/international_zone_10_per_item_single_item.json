[{"description": ["Scenario zone 10-1", "", "Test send letter to zone 10 that's within RoyalMail letter's requirement.", "Zone 10 includes United States and Canada.", "Shipped items worth £50. Letter requirement:", "", "RoyalMail restrictions: max weight: 100g, max. dim (LxWxH): 240mm x 165mm x 5mm.", "Parceforce accepts parcels up to 1500mm in length, 3000mm", "length and girth combined, and max. of 30000g per item.", ""], "contents": [{"weight": 100, "length": 230, "width": 160, "height": 5, "price": 50, "quantity": 1}], "expected_quotes": {"international-tracked": "7.90", "international-tracked-signed": "8.15", "parcelforce-globalexpress": "51.50", "parcelforce-globalpriority": "49.00", "parcelforce-globalvalue": "46.55"}}, {"description": ["Scenario zone 10-2", "", "Test send parcel to zone 10 that's within RoyalMail parcel's requirement.", "Zone 10 includes United States and Canada.", "Shipped items worth £100. Parcel requirement:", "", "RoyalMail restrictions: max weight: 2000g. ( L + W + H ) <= 900mm, no single side > 600mm.", "Parceforce accepts parcels up to 1500mm in length, 3000mm", "length and girth combined, and max. of 30000g per item.", ""], "contents": [{"weight": 2000, "length": 450, "width": 200, "height": 200, "price": 100, "quantity": 1}], "expected_quotes": {"parcelforce-globalexpress": "82.45", "parcelforce-globalpriority": "79.45", "parcelforce-globalvalue": "75.60"}}, {"description": ["Scenario zone 10-3", "", "Test send parcel to zone 10 that's above RoyalMail parcel's requirement.", "Zone 10 includes United States and Canada.", "Shipped items worth £1000.", "", "Parceforce accepts parcels up to 1500mm in length, 3000mm", "length and girth combined, and max. of 30000g per item.", ""], "contents": [{"weight": 2500, "length": 640, "width": 440, "height": 155, "price": 1000, "quantity": 1}], "expected_quotes": {"parcelforce-globalexpress": "147.50", "parcelforce-globalpriority": "144.45"}}, {"description": ["Scenario zone 10-4", "", "Test send parcel to zone 10 hat almost hits max weight (30000g).", "Volumetric weight (29840g) will be used instead of actual weight.", "Zone 10 includes United States and Canada.", "Shipped item weight 29000g.", ""], "contents": [{"weight": 29000, "length": 1480, "width": 480, "height": 210, "price": 2500, "quantity": 1}], "expected_quotes": {"parcelforce-globalexpress": "329.25", "parcelforce-globalpriority": "309.00"}}, {"description": ["Scenario zone 10-5", "", "Test send parcel to zone 10 hat almost hits max weight (30000g).", "Test send parcel to zone 10 over the maximum weight (30000g).", "Zone 10 includes United States and Canada.", "This should return empty.", ""], "contents": [{"weight": 31000, "length": 1000, "width": 500, "height": 100, "price": 2600, "quantity": 1}], "expected_quotes": {}}, {"description": ["Scenario zone 10-6", "", "Test send parcel to zone 10 that hits the limit of globalvalue compensation.", "Zone 10 includes United States and Canada.", "Shipped items worth £500. This is the max. cover for globalvalue", ""], "contents": [{"weight": 10000, "length": 1000, "width": 500, "height": 100, "price": 500, "quantity": 1}], "expected_quotes": {"parcelforce-globalvalue": "126.25", "parcelforce-globalexpress": "136.25", "parcelforce-globalpriority": "132.00"}}, {"description": ["Scenario zone 10-7", "", "Test send parcel to zone 10 that hits the limit of compensation.", "Zone 10 includes United States and Canada.", "Shipped items worth £2500. This is the max. cover for globalpriority", "and globalexpress. globalvalue won't show up in the result.", ""], "contents": [{"weight": 10000, "length": 1000, "width": 500, "height": 100, "price": 2500, "quantity": 1}], "expected_quotes": {"parcelforce-globalexpress": "211.25", "parcelforce-globalpriority": "207.00"}}, {"description": ["Scenario zone 10-8", "", "Test send parcel to zone 10 with compensation over the maximum (2500).", "Zone 10 includes United States and Canada.", "Shipped items worth £2600. This should return only globaleconomy.", ""], "contents": [{"weight": 10000, "length": 1000, "width": 500, "height": 100, "price": 2600, "quantity": 1}], "expected_quotes": {}}]