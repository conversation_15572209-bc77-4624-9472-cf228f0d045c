<?php // phpcs:ignore WordPress.Files.FileName.NotHyphenatedLowercase
/**
 * Royalmail test case class file.
 *
 * @package WC_Shipping_Royalmail
 */

/**
 * Royalmail test case class.
 */
class Royalmail_TestCase extends \PHPUnit\Framework\TestCase {

	/**
	 * Setting up the testcase.
	 */
	protected function setUp(): void {
		parent::setUp();

		\Spies\stub_function( 'plugin_dir_path' )->that_returns( '' );
		\Spies\stub_function( 'untrailingslashit' )->that_returns( '.' );

		// Test 2019 rate calculations.
		\Spies\stub_function( 'current_time' )->that_returns( strtotime( '2019-06-01' ) );
	}

	/**
	 * Create empty package to country destination.
	 *
	 * @param string $destination Country destination.
	 *
	 * @return array
	 */
	protected function get_empty_package_to( $destination ) {
		return array(
			'contents'    => array(),
			'destination' => array(
				'country' => $destination,
			),
		);
	}

	/**
	 * Get test data from the file.
	 *
	 * @param string $filename Filename.
	 *
	 * @return string
	 */
	protected function get_test_data( $filename ) {
		return json_decode(
			// phpcs:ignore WordPress.WP.AlternativeFunctions.file_get_contents_file_get_contents --- only for testing
			file_get_contents( sprintf( 'tests/phpunit/data/%s.json', $filename ) ),
			true
		);
	}

	/**
	 * Get service instance.
	 *
	 * @param string $destination Country destination.
	 * @param string $service Royalmail service.
	 * @param string $price Type of price.
	 *
	 * @return object.
	 */
	protected function get_service_instance( $destination, $service, $price = 'regular' ) {
		$rates = new WC_Shipping_Royalmail_Rates( $this->get_empty_package_to( $destination ), 'per_item', array(), '', $price );
		require_once $rates->get_service_class_path( $service );
		$classname = $rates->get_service_class_name( $service );

		return new $classname();
	}

	/**
	 * Set user defined boxes.
	 *
	 * @param array $boxes User defined boxes.
	 */
	protected function set_user_defined_boxes( array $boxes ) {
		global $royal_mail_box_sizes;

		$royal_mail_boxes_sizes = $boxes;
	}
}
